"use client";

import { useEffect, useState } from 'react';

/**
 * Safe MetaMask detection component that handles errors gracefully
 * Prevents console errors when MetaMask extension is not installed
 */
const SafeMetaMaskDetection = ({ children }) => {
  const [hasMetaMask, setHasMetaMask] = useState(false);
  const [isChecking, setIsChecking] = useState(true);

  useEffect(() => {
    const checkMetaMask = async () => {
      try {
        // Check if MetaMask is available
        if (typeof window !== 'undefined' && window.ethereum) {
          // Additional check to ensure it's actually MetaMask
          const isMetaMask = window.ethereum.isMetaMask;
          setHasMetaMask(isMetaMask || false);
        } else {
          setHasMetaMask(false);
        }
      } catch (error) {
        // Silently handle MetaMask detection errors
        console.debug('MetaMask detection failed (this is normal if MetaMask is not installed):', error.message);
        setHasMetaMask(false);
      } finally {
        setIsChecking(false);
      }
    };

    // Add a small delay to ensure window is fully loaded
    const timer = setTimeout(checkMetaMask, 100);
    
    return () => clearTimeout(timer);
  }, []);

  // Provide MetaMask context to children if needed
  if (typeof children === 'function') {
    return children({ hasMetaMask, isChecking });
  }

  return children;
};

export default SafeMetaMaskDetection;
