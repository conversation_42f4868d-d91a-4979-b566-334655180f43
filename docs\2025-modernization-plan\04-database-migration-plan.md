# Database Migration Plan

## Executive Summary

This document outlines the strategy for consolidating the current dual-database architecture (PostgreSQL + MongoDB) into a single, modern PostgreSQL database via a new Supabase project. This migration will significantly reduce complexity while leveraging Supabase's advanced features.

## Current Database Architecture Issues

### Dual Database Problems
1. **Complexity**: Managing two different database systems
2. **Consistency**: Potential data synchronization issues
3. **Performance**: Multiple connection pools and queries
4. **Maintenance**: Double the database administration overhead
5. **Development**: Developers need to understand both systems

### Current Database Usage Analysis

#### PostgreSQL (Primary Database)
- **Provider**: Supabase (existing project)
- **ORM**: Prisma
- **Usage**: Core application data
- **Tables**: Users, PhoneNumbers, Calls, Messages, Automations, Subscriptions

#### MongoDB (Secondary Database)
- **Provider**: Local/Cloud MongoDB
- **Usage**: "AI-generated content storage" (unclear necessity)
- **Data**: Limited usage based on codebase analysis

## New Database Architecture

### Single Database Strategy
- **Provider**: New Supabase project (separate from existing 'freela-syria-marketplace')
- **Database**: PostgreSQL with advanced features
- **ORM**: Prisma (enhanced configuration)
- **Additional Features**: Row Level Security, Real-time, Edge Functions

### Why New Supabase Project?
1. **Clean Start**: Fresh database without legacy data
2. **Proper Configuration**: Optimized for CallSaver's needs
3. **Security**: Proper RLS policies from the beginning
4. **Isolation**: Separate from other projects
5. **Modern Features**: Latest Supabase capabilities

## Enhanced Database Schema

### Core Tables (Migrated from Current Schema)
```sql
-- Users table with enhanced fields
CREATE TABLE users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email VARCHAR UNIQUE NOT NULL,
  name VARCHAR,
  avatar_url VARCHAR,
  phone VARCHAR,
  timezone VARCHAR DEFAULT 'UTC',
  preferences JSONB DEFAULT '{}',
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Phone numbers with enhanced metadata
CREATE TABLE phone_numbers (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  number VARCHAR UNIQUE NOT NULL,
  friendly_name VARCHAR,
  country_code VARCHAR(3) NOT NULL,
  region VARCHAR,
  locality VARCHAR,
  capabilities JSONB DEFAULT '{"voice": true, "sms": true}',
  twilio_sid VARCHAR,
  is_active BOOLEAN DEFAULT true,
  is_primary BOOLEAN DEFAULT false,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Enhanced calls table
CREATE TABLE calls (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  phone_number_id UUID REFERENCES phone_numbers(id),
  external_id VARCHAR, -- Twilio SID or ElevenLabs conversation ID
  from_number VARCHAR NOT NULL,
  to_number VARCHAR NOT NULL,
  direction VARCHAR CHECK (direction IN ('inbound', 'outbound')),
  status VARCHAR NOT NULL,
  duration_seconds INTEGER,
  price DECIMAL(10,4),
  answered_by VARCHAR,
  recording_url VARCHAR,
  transcript TEXT,
  summary TEXT,
  sentiment VARCHAR,
  ai_analysis JSONB, -- Replaces MongoDB AI data
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Enhanced messages table
CREATE TABLE messages (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  phone_number_id UUID REFERENCES phone_numbers(id),
  external_id VARCHAR, -- Twilio message SID
  from_number VARCHAR NOT NULL,
  to_number VARCHAR NOT NULL,
  direction VARCHAR CHECK (direction IN ('inbound', 'outbound')),
  body TEXT NOT NULL,
  status VARCHAR NOT NULL,
  price DECIMAL(10,4),
  media_urls TEXT[],
  ai_response JSONB, -- AI-generated response data
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

### New Tables for Enhanced Features
```sql
-- ElevenLabs agents configuration
CREATE TABLE ai_agents (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  name VARCHAR NOT NULL,
  description TEXT,
  elevenlabs_agent_id VARCHAR UNIQUE,
  prompt TEXT NOT NULL,
  voice_id VARCHAR,
  voice_settings JSONB DEFAULT '{}',
  llm_config JSONB DEFAULT '{"provider": "openai", "model": "gpt-4"}',
  conversation_config JSONB DEFAULT '{}',
  knowledge_base JSONB DEFAULT '{}',
  tools JSONB DEFAULT '[]',
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Phone number to agent assignments
CREATE TABLE phone_number_agents (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  phone_number_id UUID REFERENCES phone_numbers(id) ON DELETE CASCADE,
  ai_agent_id UUID REFERENCES ai_agents(id) ON DELETE CASCADE,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT NOW(),
  UNIQUE(phone_number_id, ai_agent_id)
);

-- Conversation analytics
CREATE TABLE conversation_analytics (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  call_id UUID REFERENCES calls(id) ON DELETE CASCADE,
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  agent_id UUID REFERENCES ai_agents(id),
  metrics JSONB NOT NULL, -- Response time, interruptions, etc.
  insights JSONB DEFAULT '{}', -- AI-generated insights
  satisfaction_score INTEGER CHECK (satisfaction_score BETWEEN 1 AND 5),
  created_at TIMESTAMP DEFAULT NOW()
);

-- Webhook logs for debugging
CREATE TABLE webhook_logs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  source VARCHAR NOT NULL, -- 'elevenlabs', 'twilio', 'stripe'
  event_type VARCHAR NOT NULL,
  payload JSONB NOT NULL,
  processed_at TIMESTAMP,
  error_message TEXT,
  retry_count INTEGER DEFAULT 0,
  created_at TIMESTAMP DEFAULT NOW()
);

-- User organizations (for team features)
CREATE TABLE organizations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR NOT NULL,
  slug VARCHAR UNIQUE NOT NULL,
  settings JSONB DEFAULT '{}',
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Organization memberships
CREATE TABLE organization_members (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  role VARCHAR DEFAULT 'member' CHECK (role IN ('owner', 'admin', 'member')),
  permissions JSONB DEFAULT '{}',
  created_at TIMESTAMP DEFAULT NOW(),
  UNIQUE(organization_id, user_id)
);
```

## Row Level Security (RLS) Policies

### User Data Isolation
```sql
-- Enable RLS on all tables
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE phone_numbers ENABLE ROW LEVEL SECURITY;
ALTER TABLE calls ENABLE ROW LEVEL SECURITY;
ALTER TABLE messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE ai_agents ENABLE ROW LEVEL SECURITY;

-- Users can only access their own data
CREATE POLICY "Users can view own profile" ON users
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON users
  FOR UPDATE USING (auth.uid() = id);

-- Phone numbers policy
CREATE POLICY "Users can manage own phone numbers" ON phone_numbers
  FOR ALL USING (auth.uid() = user_id);

-- Calls policy
CREATE POLICY "Users can view own calls" ON calls
  FOR SELECT USING (auth.uid() = user_id);

-- Messages policy
CREATE POLICY "Users can view own messages" ON messages
  FOR SELECT USING (auth.uid() = user_id);

-- AI agents policy
CREATE POLICY "Users can manage own agents" ON ai_agents
  FOR ALL USING (auth.uid() = user_id);
```

### Organization-based Access
```sql
-- Organization members can access organization data
CREATE POLICY "Organization members can view calls" ON calls
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM organization_members om
      JOIN users u ON u.id = om.user_id
      WHERE om.user_id = auth.uid()
      AND u.id = calls.user_id
    )
  );
```

## Data Migration Strategy

### Phase 1: Environment Setup
1. **Create New Supabase Project**
   - Set up new project specifically for CallSaver
   - Configure database settings and extensions
   - Set up connection pooling

2. **Configure Development Environment**
   - Update environment variables
   - Configure Prisma for new database
   - Set up migration scripts

### Phase 2: Schema Migration
1. **Create New Schema**
   - Run enhanced schema creation scripts
   - Set up RLS policies
   - Create indexes for performance

2. **Data Migration Scripts**
```javascript
// Migration script example
const migrateUserData = async () => {
  const oldUsers = await oldPrisma.user.findMany();
  
  for (const user of oldUsers) {
    await newPrisma.user.create({
      data: {
        id: user.id,
        email: user.email,
        name: user.name,
        created_at: user.createdAt,
        updated_at: user.updatedAt,
        // Map additional fields
        preferences: user.preferences || {}
      }
    });
  }
};
```

### Phase 3: MongoDB Data Migration
1. **Identify MongoDB Data**
   - Audit what data exists in MongoDB
   - Determine which data is still relevant
   - Map MongoDB documents to PostgreSQL JSON columns

2. **Migration Script**
```javascript
const migrateMongoDB = async () => {
  // Connect to MongoDB
  const mongoClient = new MongoClient(MONGODB_URI);
  await mongoClient.connect();
  
  // Migrate AI-generated content
  const aiContent = await mongoClient.db('callsaver')
    .collection('ai_content').find({}).toArray();
  
  for (const content of aiContent) {
    // Map to PostgreSQL JSON column
    await newPrisma.call.update({
      where: { id: content.callId },
      data: {
        ai_analysis: content.analysis
      }
    });
  }
};
```

### Phase 4: Application Updates
1. **Update Database Connections**
   - Replace old database URLs
   - Update Prisma client configuration
   - Test all database operations

2. **Update Authentication**
   - Configure Supabase Auth
   - Update auth middleware
   - Test authentication flows

## Performance Optimization

### Database Indexes
```sql
-- Performance indexes
CREATE INDEX idx_calls_user_id_created_at ON calls(user_id, created_at DESC);
CREATE INDEX idx_messages_user_id_created_at ON messages(user_id, created_at DESC);
CREATE INDEX idx_phone_numbers_user_id ON phone_numbers(user_id);
CREATE INDEX idx_ai_agents_user_id ON ai_agents(user_id);

-- Full-text search indexes
CREATE INDEX idx_calls_transcript_fts ON calls USING gin(to_tsvector('english', transcript));
CREATE INDEX idx_messages_body_fts ON messages USING gin(to_tsvector('english', body));
```

### Connection Pooling
```javascript
// Enhanced Prisma configuration
const prisma = new PrismaClient({
  datasources: {
    db: {
      url: process.env.DATABASE_URL,
    },
  },
  log: ['query', 'info', 'warn', 'error'],
});

// Connection pooling configuration
export const prismaConfig = {
  connectionLimit: 10,
  poolTimeout: 60000,
  idleTimeout: 600000,
};
```

## Backup and Recovery Strategy

### Automated Backups
1. **Supabase Built-in Backups**: Daily automated backups
2. **Point-in-Time Recovery**: Available for 7 days
3. **Custom Backup Scripts**: Weekly full database dumps

### Migration Rollback Plan
1. **Keep Old Database**: Maintain old database during migration
2. **Rollback Scripts**: Prepared scripts to revert changes
3. **Data Validation**: Comprehensive validation before cutover

## Testing Strategy

### Data Integrity Testing
```javascript
// Test data migration integrity
const validateMigration = async () => {
  const oldUserCount = await oldPrisma.user.count();
  const newUserCount = await newPrisma.user.count();
  
  assert(oldUserCount === newUserCount, 'User count mismatch');
  
  // Validate specific records
  const sampleUsers = await oldPrisma.user.findMany({ take: 100 });
  for (const user of sampleUsers) {
    const migratedUser = await newPrisma.user.findUnique({
      where: { id: user.id }
    });
    assert(migratedUser, `User ${user.id} not migrated`);
    assert(migratedUser.email === user.email, 'Email mismatch');
  }
};
```

### Performance Testing
1. **Load Testing**: Simulate production load
2. **Query Performance**: Measure query response times
3. **Connection Testing**: Test connection pool limits

## Migration Timeline

### Week 1: Setup and Planning
- Create new Supabase project
- Set up development environment
- Create migration scripts

### Week 2: Schema Migration
- Deploy new schema
- Set up RLS policies
- Create performance indexes

### Week 3: Data Migration
- Migrate core application data
- Migrate MongoDB data to JSON columns
- Validate data integrity

### Week 4: Application Updates
- Update database connections
- Test all application features
- Performance optimization

## Risk Mitigation

### Technical Risks
1. **Data Loss**: Comprehensive backup strategy
2. **Downtime**: Blue-green deployment approach
3. **Performance**: Thorough performance testing
4. **Compatibility**: Extensive integration testing

### Business Risks
1. **User Impact**: Minimal downtime during migration
2. **Data Consistency**: Validation at every step
3. **Rollback Plan**: Quick rollback if issues arise

## Success Metrics

### Technical Metrics
- **Migration Accuracy**: 100% data integrity
- **Performance**: <100ms average query time
- **Uptime**: >99.9% during migration
- **Error Rate**: <0.1% post-migration

### Business Metrics
- **User Experience**: No user-facing issues
- **Feature Parity**: All features working post-migration
- **Support Tickets**: No increase in database-related issues

---

**Next Document**: [05-authentication-modernization.md](./05-authentication-modernization.md)
