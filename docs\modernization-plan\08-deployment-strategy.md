# Deployment Strategy
## CallSaver.app - Containerization, CI/CD, and Production Deployment

### Executive Summary

This document outlines the comprehensive deployment strategy for CallSaver.app modernization, focusing on containerized deployments, automated CI/CD pipelines, and production-ready infrastructure. The strategy emphasizes zero-downtime deployments, rollback capabilities, and scalable architecture while maintaining the conservative approach to changes.

### Deployment Architecture Overview

#### Target Infrastructure
```mermaid
graph TB
    subgraph "Production Environment"
        LB[Load Balancer]
        subgraph "Frontend Cluster"
            FE1[Frontend Pod 1]
            FE2[Frontend Pod 2]
            FE3[Frontend Pod 3]
        end
        subgraph "Backend Cluster"
            BE1[Backend Pod 1]
            BE2[Backend Pod 2]
            BE3[Backend Pod 3]
        end
        subgraph "Data Layer"
            SUP[Supabase]
            MON[MongoDB Atlas]
            RED[Redis Cache]
        end
    end
    
    subgraph "External Services"
        TWI[Twilio]
        ELE[ElevenLabs]
        GOO[Google OAuth]
    end
    
    LB --> FE1
    LB --> FE2
    LB --> FE3
    FE1 --> BE1
    FE2 --> BE2
    FE3 --> BE3
    BE1 --> SUP
    BE2 --> MON
    BE3 --> RED
    BE1 --> TWI
    BE2 --> ELE
    BE3 --> GOO
```

#### Environment Strategy
```typescript
// Environment configuration structure
interface EnvironmentConfig {
  name: 'development' | 'staging' | 'production'
  domain: string
  replicas: {
    frontend: number
    backend: number
  }
  resources: {
    cpu: string
    memory: string
  }
  scaling: {
    minReplicas: number
    maxReplicas: number
    targetCPU: number
  }
}

const environments: Record<string, EnvironmentConfig> = {
  development: {
    name: 'development',
    domain: 'dev.callsaver.app',
    replicas: { frontend: 1, backend: 1 },
    resources: { cpu: '100m', memory: '256Mi' },
    scaling: { minReplicas: 1, maxReplicas: 2, targetCPU: 70 }
  },
  staging: {
    name: 'staging',
    domain: 'staging.callsaver.app',
    replicas: { frontend: 2, backend: 2 },
    resources: { cpu: '200m', memory: '512Mi' },
    scaling: { minReplicas: 2, maxReplicas: 4, targetCPU: 60 }
  },
  production: {
    name: 'production',
    domain: 'callsaver.app',
    replicas: { frontend: 3, backend: 3 },
    resources: { cpu: '500m', memory: '1Gi' },
    scaling: { minReplicas: 3, maxReplicas: 10, targetCPU: 50 }
  }
}
```

### Containerization Strategy

#### Frontend Dockerfile
```dockerfile
# frontend/Dockerfile
# Multi-stage build for optimal image size
FROM node:20-alpine AS base
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production && npm cache clean --force

FROM node:20-alpine AS build
WORKDIR /app
COPY package*.json ./
RUN npm ci
COPY . .
RUN npm run build

FROM node:20-alpine AS runtime
WORKDIR /app

# Create non-root user for security
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nextjs -u 1001

# Copy built application
COPY --from=build /app/public ./public
COPY --from=build --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=build --chown=nextjs:nodejs /app/.next/static ./.next/static

USER nextjs

EXPOSE 3000
ENV PORT 3000
ENV HOSTNAME "0.0.0.0"

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3000/api/health || exit 1

CMD ["node", "server.js"]
```

#### Backend Dockerfile
```dockerfile
# backend/Dockerfile
FROM node:20-alpine AS base
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production && npm cache clean --force

FROM node:20-alpine AS build
WORKDIR /app
COPY package*.json ./
RUN npm ci
COPY . .
RUN npm run build

FROM node:20-alpine AS runtime
WORKDIR /app

# Install dumb-init for proper signal handling
RUN apk add --no-cache dumb-init

# Create non-root user
RUN addgroup -g 1001 -S nodejs
RUN adduser -S apiuser -u 1001

# Copy application files
COPY --from=build --chown=apiuser:nodejs /app/dist ./dist
COPY --from=base --chown=apiuser:nodejs /app/node_modules ./node_modules

USER apiuser

EXPOSE 3001
ENV PORT 3001
ENV NODE_ENV production

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=10s --retries=3 \
  CMD curl -f http://localhost:3001/api/v2/health || exit 1

ENTRYPOINT ["dumb-init", "--"]
CMD ["node", "dist/server.js"]
```

#### Docker Compose for Development
```yaml
# docker-compose.yml
version: '3.8'

services:
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      target: runtime
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
      - NEXT_PUBLIC_API_URL=http://backend:3001
      - NEXT_PUBLIC_SUPABASE_URL=${NEXT_PUBLIC_SUPABASE_URL}
      - NEXT_PUBLIC_SUPABASE_ANON_KEY=${NEXT_PUBLIC_SUPABASE_ANON_KEY}
    depends_on:
      - backend
    volumes:
      - ./frontend:/app
      - /app/node_modules
      - /app/.next
    networks:
      - callsaver-network

  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
      target: runtime
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=development
      - DATABASE_URL=${DATABASE_URL}
      - SUPABASE_SERVICE_ROLE_KEY=${SUPABASE_SERVICE_ROLE_KEY}
      - TWILIO_ACCOUNT_SID=${TWILIO_ACCOUNT_SID}
      - TWILIO_AUTH_TOKEN=${TWILIO_AUTH_TOKEN}
      - ELEVENLABS_API_KEY=${ELEVENLABS_API_KEY}
    volumes:
      - ./backend:/app
      - /app/node_modules
      - /app/dist
    networks:
      - callsaver-network

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - callsaver-network

  postgres:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=callsaver_dev
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - callsaver-network

volumes:
  redis_data:
  postgres_data:

networks:
  callsaver-network:
    driver: bridge
```

### Kubernetes Deployment Configuration

#### Frontend Deployment
```yaml
# k8s/frontend-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: callsaver-frontend
  namespace: callsaver
  labels:
    app: callsaver-frontend
    version: v1
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: callsaver-frontend
  template:
    metadata:
      labels:
        app: callsaver-frontend
        version: v1
    spec:
      containers:
      - name: frontend
        image: callsaver/frontend:latest
        ports:
        - containerPort: 3000
        env:
        - name: NODE_ENV
          value: "production"
        - name: NEXT_PUBLIC_API_URL
          value: "https://api.callsaver.app"
        - name: NEXT_PUBLIC_SUPABASE_URL
          valueFrom:
            secretKeyRef:
              name: callsaver-secrets
              key: supabase-url
        - name: NEXT_PUBLIC_SUPABASE_ANON_KEY
          valueFrom:
            secretKeyRef:
              name: callsaver-secrets
              key: supabase-anon-key
        resources:
          requests:
            memory: "256Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /api/health
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /api/health
            port: 3000
          initialDelaySeconds: 5
          periodSeconds: 5
        securityContext:
          runAsNonRoot: true
          runAsUser: 1001
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          capabilities:
            drop:
            - ALL

---
apiVersion: v1
kind: Service
metadata:
  name: callsaver-frontend-service
  namespace: callsaver
spec:
  selector:
    app: callsaver-frontend
  ports:
  - protocol: TCP
    port: 80
    targetPort: 3000
  type: ClusterIP

---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: callsaver-frontend-hpa
  namespace: callsaver
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: callsaver-frontend
  minReplicas: 3
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 50
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 70
```

#### Backend Deployment
```yaml
# k8s/backend-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: callsaver-backend
  namespace: callsaver
  labels:
    app: callsaver-backend
    version: v1
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: callsaver-backend
  template:
    metadata:
      labels:
        app: callsaver-backend
        version: v1
    spec:
      containers:
      - name: backend
        image: callsaver/backend:latest
        ports:
        - containerPort: 3001
        env:
        - name: NODE_ENV
          value: "production"
        - name: PORT
          value: "3001"
        - name: SUPABASE_SERVICE_ROLE_KEY
          valueFrom:
            secretKeyRef:
              name: callsaver-secrets
              key: supabase-service-role-key
        - name: TWILIO_ACCOUNT_SID
          valueFrom:
            secretKeyRef:
              name: callsaver-secrets
              key: twilio-account-sid
        - name: TWILIO_AUTH_TOKEN
          valueFrom:
            secretKeyRef:
              name: callsaver-secrets
              key: twilio-auth-token
        - name: ELEVENLABS_API_KEY
          valueFrom:
            secretKeyRef:
              name: callsaver-secrets
              key: elevenlabs-api-key
        - name: REDIS_URL
          value: "redis://callsaver-redis-service:6379"
        resources:
          requests:
            memory: "512Mi"
            cpu: "200m"
          limits:
            memory: "1Gi"
            cpu: "1000m"
        livenessProbe:
          httpGet:
            path: /api/v2/health
            port: 3001
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /api/v2/health
            port: 3001
          initialDelaySeconds: 10
          periodSeconds: 5
        securityContext:
          runAsNonRoot: true
          runAsUser: 1001
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          capabilities:
            drop:
            - ALL

---
apiVersion: v1
kind: Service
metadata:
  name: callsaver-backend-service
  namespace: callsaver
spec:
  selector:
    app: callsaver-backend
  ports:
  - protocol: TCP
    port: 80
    targetPort: 3001
  type: ClusterIP
```

### CI/CD Pipeline Configuration

#### GitHub Actions Workflow
```yaml
# .github/workflows/deploy.yml
name: Deploy to Production

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

env:
  REGISTRY: ghcr.io
  IMAGE_NAME_FRONTEND: ${{ github.repository }}/frontend
  IMAGE_NAME_BACKEND: ${{ github.repository }}/backend

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'

      - name: Install dependencies
        run: |
          cd frontend && npm ci
          cd ../backend && npm ci

      - name: Run tests
        run: |
          cd frontend && npm run test:ci
          cd ../backend && npm run test:ci

      - name: Run security audit
        run: |
          cd frontend && npm audit --audit-level moderate
          cd ../backend && npm audit --audit-level moderate

  build-and-push:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    permissions:
      contents: read
      packages: write

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Log in to Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Extract metadata (frontend)
        id: meta-frontend
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME_FRONTEND }}
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=sha,prefix={{branch}}-
            type=raw,value=latest,enable={{is_default_branch}}

      - name: Build and push frontend image
        uses: docker/build-push-action@v5
        with:
          context: ./frontend
          push: true
          tags: ${{ steps.meta-frontend.outputs.tags }}
          labels: ${{ steps.meta-frontend.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

      - name: Extract metadata (backend)
        id: meta-backend
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME_BACKEND }}
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=sha,prefix={{branch}}-
            type=raw,value=latest,enable={{is_default_branch}}

      - name: Build and push backend image
        uses: docker/build-push-action@v5
        with:
          context: ./backend
          push: true
          tags: ${{ steps.meta-backend.outputs.tags }}
          labels: ${{ steps.meta-backend.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

  deploy-staging:
    needs: build-and-push
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    environment: staging

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Configure kubectl
        uses: azure/k8s-set-context@v3
        with:
          method: kubeconfig
          kubeconfig: ${{ secrets.KUBE_CONFIG }}

      - name: Deploy to staging
        run: |
          # Update image tags in deployment files
          sed -i "s|image: callsaver/frontend:.*|image: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME_FRONTEND }}:${{ github.sha }}|" k8s/staging/frontend-deployment.yaml
          sed -i "s|image: callsaver/backend:.*|image: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME_BACKEND }}:${{ github.sha }}|" k8s/staging/backend-deployment.yaml
          
          # Apply configurations
          kubectl apply -f k8s/staging/

      - name: Wait for deployment
        run: |
          kubectl rollout status deployment/callsaver-frontend -n callsaver-staging --timeout=300s
          kubectl rollout status deployment/callsaver-backend -n callsaver-staging --timeout=300s

      - name: Run smoke tests
        run: |
          # Wait for services to be ready
          sleep 30
          
          # Run basic health checks
          curl -f https://staging.callsaver.app/api/health
          curl -f https://api-staging.callsaver.app/api/v2/health

  deploy-production:
    needs: deploy-staging
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    environment: production

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Configure kubectl
        uses: azure/k8s-set-context@v3
        with:
          method: kubeconfig
          kubeconfig: ${{ secrets.KUBE_CONFIG_PROD }}

      - name: Deploy to production
        run: |
          # Update image tags
          sed -i "s|image: callsaver/frontend:.*|image: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME_FRONTEND }}:${{ github.sha }}|" k8s/production/frontend-deployment.yaml
          sed -i "s|image: callsaver/backend:.*|image: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME_BACKEND }}:${{ github.sha }}|" k8s/production/backend-deployment.yaml
          
          # Apply with rolling update strategy
          kubectl apply -f k8s/production/

      - name: Monitor deployment
        run: |
          # Monitor rollout status
          kubectl rollout status deployment/callsaver-frontend -n callsaver --timeout=600s
          kubectl rollout status deployment/callsaver-backend -n callsaver --timeout=600s
          
          # Verify health endpoints
          sleep 60
          curl -f https://callsaver.app/api/health
          curl -f https://api.callsaver.app/api/v2/health

      - name: Notify deployment success
        if: success()
        uses: 8398a7/action-slack@v3
        with:
          status: success
          text: "✅ CallSaver.app deployed successfully to production"
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

      - name: Notify deployment failure
        if: failure()
        uses: 8398a7/action-slack@v3
        with:
          status: failure
          text: "❌ CallSaver.app deployment to production failed"
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
```

### Blue-Green Deployment Strategy

#### Blue-Green Configuration
```yaml
# k8s/blue-green/service-selector.yaml
apiVersion: v1
kind: Service
metadata:
  name: callsaver-frontend-active
  namespace: callsaver
spec:
  selector:
    app: callsaver-frontend
    version: blue  # Switch between 'blue' and 'green'
  ports:
  - protocol: TCP
    port: 80
    targetPort: 3000
  type: ClusterIP

---
apiVersion: v1
kind: Service
metadata:
  name: callsaver-backend-active
  namespace: callsaver
spec:
  selector:
    app: callsaver-backend
    version: blue  # Switch between 'blue' and 'green'
  ports:
  - protocol: TCP
    port: 80
    targetPort: 3001
  type: ClusterIP
```

#### Deployment Script
```bash
#!/bin/bash
# scripts/blue-green-deploy.sh

set -e

NAMESPACE="callsaver"
NEW_VERSION=$1
CURRENT_VERSION=$(kubectl get service callsaver-frontend-active -n $NAMESPACE -o jsonpath='{.spec.selector.version}')

if [ "$CURRENT_VERSION" = "blue" ]; then
    NEW_COLOR="green"
    OLD_COLOR="blue"
else
    NEW_COLOR="blue"
    OLD_COLOR="green"
fi

echo "Current version: $OLD_COLOR"
echo "Deploying to: $NEW_COLOR"

# Update deployment with new version
sed -i "s/version: .*/version: $NEW_COLOR/" k8s/production/frontend-deployment.yaml
sed -i "s/version: .*/version: $NEW_COLOR/" k8s/production/backend-deployment.yaml
sed -i "s/image: .*/image: callsaver\/frontend:$NEW_VERSION/" k8s/production/frontend-deployment.yaml
sed -i "s/image: .*/image: callsaver\/backend:$NEW_VERSION/" k8s/production/backend-deployment.yaml

# Deploy new version
kubectl apply -f k8s/production/

# Wait for deployment to be ready
kubectl rollout status deployment/callsaver-frontend -n $NAMESPACE --timeout=300s
kubectl rollout status deployment/callsaver-backend -n $NAMESPACE --timeout=300s

# Run health checks on new version
echo "Running health checks on $NEW_COLOR version..."
kubectl port-forward service/callsaver-frontend-$NEW_COLOR 8080:80 -n $NAMESPACE &
PF_PID=$!
sleep 10

if curl -f http://localhost:8080/api/health; then
    echo "Health check passed. Switching traffic to $NEW_COLOR"
    
    # Switch traffic to new version
    kubectl patch service callsaver-frontend-active -n $NAMESPACE -p '{"spec":{"selector":{"version":"'$NEW_COLOR'"}}}'
    kubectl patch service callsaver-backend-active -n $NAMESPACE -p '{"spec":{"selector":{"version":"'$NEW_COLOR'"}}}'
    
    echo "Traffic switched to $NEW_COLOR version"
    echo "Old $OLD_COLOR version is still running for rollback if needed"
else
    echo "Health check failed. Keeping traffic on $OLD_COLOR version"
    kill $PF_PID
    exit 1
fi

kill $PF_PID
```

### Rollback Procedures

#### Automated Rollback Script
```bash
#!/bin/bash
# scripts/rollback.sh

set -e

NAMESPACE="callsaver"
CURRENT_VERSION=$(kubectl get service callsaver-frontend-active -n $NAMESPACE -o jsonpath='{.spec.selector.version}')

if [ "$CURRENT_VERSION" = "blue" ]; then
    ROLLBACK_TO="green"
else
    ROLLBACK_TO="blue"
fi

echo "Rolling back from $CURRENT_VERSION to $ROLLBACK_TO"

# Switch traffic back
kubectl patch service callsaver-frontend-active -n $NAMESPACE -p '{"spec":{"selector":{"version":"'$ROLLBACK_TO'"}}}'
kubectl patch service callsaver-backend-active -n $NAMESPACE -p '{"spec":{"selector":{"version":"'$ROLLBACK_TO'"}}}'

# Verify rollback
sleep 30
if curl -f https://callsaver.app/api/health; then
    echo "✅ Rollback successful. Traffic switched to $ROLLBACK_TO"
    
    # Send notification
    curl -X POST -H 'Content-type: application/json' \
        --data '{"text":"🔄 CallSaver.app rolled back to previous version successfully"}' \
        $SLACK_WEBHOOK_URL
else
    echo "❌ Rollback failed. Manual intervention required."
    exit 1
fi
```

### Environment Management

#### Environment Configuration
```typescript
// scripts/deploy-config.ts
interface DeploymentConfig {
  environment: string
  domain: string
  replicas: number
  resources: {
    requests: { cpu: string; memory: string }
    limits: { cpu: string; memory: string }
  }
  secrets: string[]
  healthCheck: {
    initialDelaySeconds: number
    periodSeconds: number
    timeoutSeconds: number
  }
}

const deploymentConfigs: Record<string, DeploymentConfig> = {
  development: {
    environment: 'development',
    domain: 'dev.callsaver.app',
    replicas: 1,
    resources: {
      requests: { cpu: '100m', memory: '256Mi' },
      limits: { cpu: '500m', memory: '512Mi' }
    },
    secrets: ['dev-secrets'],
    healthCheck: {
      initialDelaySeconds: 10,
      periodSeconds: 30,
      timeoutSeconds: 5
    }
  },
  staging: {
    environment: 'staging',
    domain: 'staging.callsaver.app',
    replicas: 2,
    resources: {
      requests: { cpu: '200m', memory: '512Mi' },
      limits: { cpu: '1000m', memory: '1Gi' }
    },
    secrets: ['staging-secrets'],
    healthCheck: {
      initialDelaySeconds: 15,
      periodSeconds: 20,
      timeoutSeconds: 5
    }
  },
  production: {
    environment: 'production',
    domain: 'callsaver.app',
    replicas: 3,
    resources: {
      requests: { cpu: '500m', memory: '1Gi' },
      limits: { cpu: '2000m', memory: '2Gi' }
    },
    secrets: ['production-secrets'],
    healthCheck: {
      initialDelaySeconds: 30,
      periodSeconds: 10,
      timeoutSeconds: 3
    }
  }
}

export function generateDeploymentManifest(env: string): string {
  const config = deploymentConfigs[env]
  if (!config) {
    throw new Error(`Unknown environment: ${env}`)
  }

  return `
apiVersion: apps/v1
kind: Deployment
metadata:
  name: callsaver-frontend-${env}
  namespace: callsaver-${env}
spec:
  replicas: ${config.replicas}
  template:
    spec:
      containers:
      - name: frontend
        resources:
          requests:
            cpu: ${config.resources.requests.cpu}
            memory: ${config.resources.requests.memory}
          limits:
            cpu: ${config.resources.limits.cpu}
            memory: ${config.resources.limits.memory}
        livenessProbe:
          initialDelaySeconds: ${config.healthCheck.initialDelaySeconds}
          periodSeconds: ${config.healthCheck.periodSeconds}
          timeoutSeconds: ${config.healthCheck.timeoutSeconds}
  `
}
```

### Deployment Timeline Integration

#### Week-by-Week Deployment Schedule
| Week | Environment | Activities | Success Criteria |
|------|-------------|------------|------------------|
| 8-9  | Development | Container setup, local testing | All services containerized |
| 10   | Staging | CI/CD pipeline, staging deployment | Automated deployments working |
| 11   | Pre-Production | Blue-green setup, load testing | Zero-downtime deployments |
| 12-14| Production | Gradual rollout, monitoring | Production stability >99.9% |

### Success Metrics

#### Deployment KPIs
- **Deployment Frequency**: Daily deployments to staging, weekly to production
- **Lead Time**: <2 hours from commit to production
- **Deployment Success Rate**: >95%
- **Mean Time to Recovery**: <15 minutes
- **Zero Downtime**: 100% of deployments with zero user impact

#### Monitoring and Alerting
```typescript
// Deployment monitoring configuration
const deploymentMetrics = {
  deploymentDuration: {
    target: 300, // 5 minutes
    alert: 600   // 10 minutes
  },
  healthCheckSuccess: {
    target: 100, // 100%
    alert: 95    // 95%
  },
  rollbackFrequency: {
    target: 0,   // 0 per week
    alert: 1     // 1 per week
  }
}
```

This deployment strategy ensures reliable, scalable, and maintainable deployments for the CallSaver.app modernization project while maintaining the conservative approach and enabling rapid iteration.
