'use client';

import { useState, useEffect } from 'react';
import { loadStripe } from '@stripe/stripe-js';

// Test page to verify CSP configuration
export default function TestCSPPage() {
  const [cspStatus, setCspStatus] = useState('Testing...');
  const [stripeStatus, setStripeStatus] = useState('Testing...');
  const [errors, setErrors] = useState([]);

  useEffect(() => {
    const testCSP = async () => {
      const testResults = [];
      
      // Test 1: Basic script execution
      try {
        eval('1 + 1'); // This should work with 'unsafe-eval'
        testResults.push('✅ Basic script execution: PASS');
      } catch (error) {
        testResults.push('❌ Basic script execution: FAIL - ' + error.message);
      }

      // Test 2: Stripe loading
      try {
        if (process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY) {
          const stripe = await loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY);
          if (stripe) {
            setStripeStatus('✅ Stripe loaded successfully');
          } else {
            setStripeStatus('❌ Stripe failed to load');
          }
        } else {
          setStripeStatus('⚠️ Stripe publishable key not configured');
        }
      } catch (error) {
        setStripeStatus('❌ Stripe error: ' + error.message);
        testResults.push('❌ Stripe loading: FAIL - ' + error.message);
      }

      // Test 3: Check for CSP violations in console
      const originalError = console.error;
      const cspErrors = [];
      console.error = (...args) => {
        const message = args.join(' ');
        if (message.includes('Content Security Policy') || message.includes('CSP')) {
          cspErrors.push(message);
        }
        originalError.apply(console, args);
      };

      // Wait a bit to catch any CSP errors
      setTimeout(() => {
        console.error = originalError;
        if (cspErrors.length === 0) {
          setCspStatus('✅ No CSP violations detected');
        } else {
          setCspStatus('❌ CSP violations detected');
          setErrors(cspErrors);
        }
      }, 2000);

      // Test 4: Dynamic script injection (should work with nonce)
      try {
        const script = document.createElement('script');
        script.textContent = 'window.testCSPScript = true;';
        document.head.appendChild(script);
        
        if (window.testCSPScript) {
          testResults.push('✅ Dynamic script injection: PASS');
        } else {
          testResults.push('❌ Dynamic script injection: FAIL');
        }
        
        document.head.removeChild(script);
        delete window.testCSPScript;
      } catch (error) {
        testResults.push('❌ Dynamic script injection: FAIL - ' + error.message);
      }

      console.log('CSP Test Results:', testResults);
    };

    testCSP();
  }, []);

  return (
    <div className="min-h-screen bg-[#0d0d17] text-white p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">CSP Configuration Test</h1>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* CSP Status */}
          <div className="bg-gray-800/50 rounded-xl border border-purple-500/20 p-6">
            <h2 className="text-xl font-semibold mb-4">CSP Status</h2>
            <p className="text-lg">{cspStatus}</p>
            
            {errors.length > 0 && (
              <div className="mt-4">
                <h3 className="text-lg font-semibold text-red-400 mb-2">CSP Errors:</h3>
                <div className="space-y-2">
                  {errors.map((error, index) => (
                    <div key={index} className="bg-red-900/20 border border-red-500/30 rounded p-2 text-sm">
                      {error}
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Stripe Status */}
          <div className="bg-gray-800/50 rounded-xl border border-purple-500/20 p-6">
            <h2 className="text-xl font-semibold mb-4">Stripe Integration</h2>
            <p className="text-lg">{stripeStatus}</p>
            
            <div className="mt-4 text-sm text-gray-400">
              <p>Publishable Key: {process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY ? 'Configured' : 'Not configured'}</p>
            </div>
          </div>

          {/* CSP Headers Info */}
          <div className="bg-gray-800/50 rounded-xl border border-purple-500/20 p-6 md:col-span-2">
            <h2 className="text-xl font-semibold mb-4">Current CSP Configuration</h2>
            <div className="text-sm text-gray-300 space-y-2">
              <p><strong>script-src:</strong> 'self' 'unsafe-eval' 'unsafe-inline' 'nonce-*' https://*.stripe.com https://m.stripe.network https://js.stripe.com https://q.stripe.com</p>
              <p><strong>style-src:</strong> 'self' 'unsafe-inline' https://fonts.googleapis.com</p>
              <p><strong>connect-src:</strong> 'self' https://*.stripe.com https://m.stripe.network https://api.stripe.com https://q.stripe.com wss: ws: https://*.supabase.co wss://*.supabase.co</p>
              <p><strong>img-src:</strong> 'self' data: blob: https://*.stripe.com [and other domains]</p>
            </div>
          </div>

          {/* Test Actions */}
          <div className="bg-gray-800/50 rounded-xl border border-purple-500/20 p-6 md:col-span-2">
            <h2 className="text-xl font-semibold mb-4">Manual Tests</h2>
            <div className="space-y-4">
              <button
                onClick={() => {
                  try {
                    // Test inline event handler
                    alert('Inline event handler works!');
                  } catch (error) {
                    alert('Inline event handler failed: ' + error.message);
                  }
                }}
                className="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors mr-4"
              >
                Test Inline Event Handler
              </button>
              
              <button
                onClick={() => {
                  // Test dynamic script creation
                  const script = document.createElement('script');
                  script.textContent = 'alert("Dynamic script executed!");';
                  try {
                    document.head.appendChild(script);
                    document.head.removeChild(script);
                  } catch (error) {
                    alert('Dynamic script failed: ' + error.message);
                  }
                }}
                className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
              >
                Test Dynamic Script
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
