import { Inter } from 'next/font/google';
import { headers } from 'next/headers';

import './globals.css';

import ClientServiceWorkerManager from './components/ClientServiceWorkerManager';
import ConditionalNavbar from './components/ConditionalNavbar';
import ConsoleErrorSuppressor from './components/ConsoleErrorSuppressor';
import GlobalBackgroundOverlay from './components/GlobalBackgroundOverlay';
import SafeMetaMaskDetection from './components/SafeMetaMaskDetection';
import { LanguageProvider } from './i18n/LanguageContext';
import { SessionProvider } from './providers/SessionProvider';

const inter = Inter({ subsets: ['latin'] });

export const metadata = {
  metadataBase: new URL(process.env.NEXT_PUBLIC_SITE_URL || 'https://callsaver.app'),
  title: 'CallSaver - Never Miss A Customer Call Again',
  description: 'AI-Powered Call Management Platform that automates your calls with advanced AI, saving you time and ensuring you never miss important information.',
  keywords: ['AI call management', 'automated calls', 'business phone system', 'call automation', 'voice AI'],
  authors: [{ name: 'CallSaver Team' }],
  creator: 'CallSaver',
  publisher: 'CallSaver',
  icons: {
    icon: '/favicon.svg',
    shortcut: '/favicon.svg',
    apple: '/favicon.svg',
  },
  openGraph: {
    title: 'CallSaver - Never Miss A Customer Call Again',
    description: 'AI-Powered Call Management Platform that automates your calls with advanced AI, saving you time and ensuring you never miss important information.',
    type: 'website',
    siteName: 'CallSaver',
    images: [
      {
        url: '/og-image.jpg',
        width: 1200,
        height: 630,
        alt: 'CallSaver - AI-Powered Call Management Platform',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'CallSaver - Never Miss A Customer Call Again',
    description: 'AI-Powered Call Management Platform that automates your calls with advanced AI.',
    images: ['/og-image.jpg'],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
};

export const viewport = {
  width: 'device-width',
  initialScale: 1,
  themeColor: '#0d0d17',
};

export const revalidate = 3600;

export default function RootLayout({ children }) {
  // Get nonce from headers for CSP
  const headersList = headers();
  const nonce = headersList.get('x-nonce') || '';

  return (
    <html lang="en" className="scroll-smooth" suppressHydrationWarning={true}>
      <head>
        {nonce && (
          <meta name="csp-nonce" content={nonce} />
        )}
      </head>
      <body className={`${inter.className} bg-[#0d0d17] min-h-screen overflow-x-hidden`} suppressHydrationWarning={true}>
        <GlobalBackgroundOverlay />
        <ClientServiceWorkerManager />
        <ConsoleErrorSuppressor />
        <SafeMetaMaskDetection>
          <SessionProvider>
            <LanguageProvider>
              <div className="min-h-screen flex flex-col relative z-10">
                <ConditionalNavbar />
                <main className="flex-grow relative">{children}</main>
              </div>
            </LanguageProvider>
          </SessionProvider>
        </SafeMetaMaskDetection>
      </body>
    </html>
  );
}
