/** @type {import('next').NextConfig} */

// Import bundle analyzer with more detailed configuration
const withBundleAnalyzer = process.env.ANALYZE === 'true'
  ? require('@next/bundle-analyzer')({
      enabled: true,
      openAnalyzer: true,
      generateStatsFile: true,
      statsFilename: 'stats.json',
    })
  : (config) => config;

// Compression for smaller production bundles
const CompressionPlugin = require('compression-webpack-plugin');

const nextConfig = {
  // Optimize image processing
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'tools.applemediaservices.com',
      },
      {
        protocol: 'https',
        hostname: 'play.google.com',
      },
      {
        protocol: 'https',
        hostname: 'randomuser.me',
      },
      {
        protocol: 'https',
        hostname: 'upload.wikimedia.org',
      },
      {
        protocol: 'https',
        hostname: 'cdn.worldvectorlogo.com',
      },
      {
        protocol: 'https',
        hostname: 'placehold.co',
      }
    ],
    // Enable image optimization quality settings
    formats: ['image/avif', 'image/webp'],
    minimumCacheTTL: 60 * 60 * 24 * 7, // 7 days
  },
  reactStrictMode: true,
  output: 'standalone',
  poweredByHeader: false, // Remove X-Powered-By header for security

  // Add HTTP cache header settings
  async headers() {
    return [
      {
        source: '/:path*',
        headers: [
          {
            key: 'X-DNS-Prefetch-Control',
            value: 'on'
          },
          {
            key: 'Strict-Transport-Security',
            value: 'max-age=63072000; includeSubDomains; preload'
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff'
          },
          {
            key: 'X-Frame-Options',
            value: 'SAMEORIGIN'
          },
          {
            key: 'Referrer-Policy',
            value: 'origin-when-cross-origin'
          },

        ]
      },
      // Cache static assets longer
      {
        source: '/static/:path*',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=31536000, immutable'
          }
        ]
      },
      // Cache images
      {
        source: '/_next/image/:path*',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=86400, stale-while-revalidate=31536000'
          }
        ]
      }
    ]
  },

  // Add rewrites to proxy API calls to backend server
  async rewrites() {
    return [
      {
        source: '/api/:path*', // Match all API routes
        destination: process.env.BACKEND_API_URL || 'http://localhost:3006/api/:path*', // Use environment variable or fallback to local backend URL
      },
    ]
  },

  // Compiler options for faster builds
  compiler: {
    removeConsole: process.env.NODE_ENV === 'production' ? {exclude: ['error', 'warn']} : false,
  },

  // Specify typescript checker to run in its own thread
  typescript: {
    ignoreBuildErrors: false,
    tsconfigPath: './tsconfig.json',
  },

  // Enhanced webpack config with performance optimizations
  webpack: (config, { dev, isServer }) => {
    // Production optimizations
    if (!dev && !isServer) {
      // Set performance budgets
      config.performance = {
        hints: 'warning',
        maxEntrypointSize: 400000, // 400kb
        maxAssetSize: 300000, // 300kb
      };

      // Add compression plugin in production
      config.plugins.push(
        new CompressionPlugin({
          algorithm: 'gzip',
          test: /\.(js|css|html|svg)$/,
          threshold: 10240, // Only compress files > 10kb
          minRatio: 0.8, // Only compress if compression ratio is at least 0.8
        })
      );
    }

    // Return the modified config
    return config;
  },
  experimental: {
    optimizeCss: false, // Disable CSS optimization to avoid critters dependency issue
    optimizePackageImports: ['lucide-react', '@heroicons/react', 'date-fns'], // Optimize large packages
  },
};

module.exports = withBundleAnalyzer(nextConfig);
