'use client';

import { useEffect } from 'react';
import { headers } from 'next/headers';

// Client-side hook to get nonce from meta tag
export function useNonce() {
  if (typeof window !== 'undefined') {
    const metaTag = document.querySelector('meta[name="csp-nonce"]');
    return metaTag?.getAttribute('content') || '';
  }
  return '';
}

// Server-side function to get nonce from headers
export function getNonce() {
  try {
    const headersList = headers();
    return headersList.get('x-nonce') || '';
  } catch (error) {
    // Fallback for client-side or when headers are not available
    return '';
  }
}

// Component to inject nonce into meta tag (safer version without dangerouslySetInnerHTML)
export function NonceScript({ nonce }) {
  if (!nonce) return null;

  // Use useEffect to set the meta tag safely
  useEffect(() => {
    if (typeof document !== 'undefined') {
      const meta = document.createElement('meta');
      meta.name = 'csp-nonce';
      meta.content = nonce;
      document.head.appendChild(meta);

      // Cleanup function
      return () => {
        const existingMeta = document.querySelector('meta[name="csp-nonce"]');
        if (existingMeta) {
          existingMeta.remove();
        }
      };
    }
  }, [nonce]);

  return null; // This component doesn't render anything
}
