# Testing Strategy
## CallSaver.app Modernization Project

### Executive Summary

This document outlines the comprehensive testing strategy for the CallSaver.app modernization project, ensuring quality, reliability, and performance throughout the transformation process. The strategy emphasizes automated testing, continuous integration, and risk-based testing approaches.

### Testing Philosophy

#### Core Principles
1. **Shift-Left Testing** - Early detection and prevention of defects
2. **Risk-Based Testing** - Focus on high-risk areas and critical user journeys
3. **Automation-First** - Maximize automated test coverage for efficiency
4. **Continuous Testing** - Integrated testing throughout the development lifecycle
5. **User-Centric** - Prioritize user experience and business value

#### Quality Gates
```typescript
// Quality gate criteria
const qualityGates = {
  unitTests: {
    coverage: 85,
    passingRate: 100
  },
  integrationTests: {
    coverage: 75,
    passingRate: 100
  },
  e2eTests: {
    criticalFlows: 100,
    passingRate: 95
  },
  performance: {
    pageLoadTime: 2000, // ms
    apiResponseTime: 200, // ms
    errorRate: 1 // %
  },
  security: {
    vulnerabilities: 0, // critical/high
    codeQuality: 'A'
  }
}
```

### Testing Pyramid Strategy

#### Level 1: Unit Testing (70% of tests)

**Scope**: Individual components, functions, and services
**Tools**: Jest, React Testing Library, Vitest
**Target Coverage**: 85%

```typescript
// Example unit test for authentication service
// __tests__/services/AuthService.test.ts
import { AuthService } from '@/services/AuthService'
import { createClient } from '@supabase/supabase-js'

jest.mock('@supabase/supabase-js')

describe('AuthService', () => {
  let authService: AuthService
  let mockSupabase: jest.Mocked<ReturnType<typeof createClient>>

  beforeEach(() => {
    mockSupabase = {
      auth: {
        signInWithOAuth: jest.fn(),
        signOut: jest.fn(),
        getUser: jest.fn(),
        onAuthStateChange: jest.fn()
      }
    } as any

    ;(createClient as jest.Mock).mockReturnValue(mockSupabase)
    authService = new AuthService()
  })

  describe('signInWithGoogle', () => {
    test('should call Supabase OAuth with correct parameters', async () => {
      mockSupabase.auth.signInWithOAuth.mockResolvedValue({
        data: { url: 'https://oauth-url.com' },
        error: null
      })

      await authService.signInWithGoogle()

      expect(mockSupabase.auth.signInWithOAuth).toHaveBeenCalledWith({
        provider: 'google',
        options: {
          redirectTo: expect.stringContaining('/auth/callback')
        }
      })
    })

    test('should throw error when OAuth fails', async () => {
      mockSupabase.auth.signInWithOAuth.mockResolvedValue({
        data: null,
        error: { message: 'OAuth failed' }
      })

      await expect(authService.signInWithGoogle()).rejects.toThrow('OAuth failed')
    })
  })

  describe('getCurrentUser', () => {
    test('should return user when authenticated', async () => {
      const mockUser = { id: '123', email: '<EMAIL>' }
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: mockUser },
        error: null
      })

      const user = await authService.getCurrentUser()

      expect(user).toEqual(mockUser)
    })

    test('should return null when not authenticated', async () => {
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: null },
        error: null
      })

      const user = await authService.getCurrentUser()

      expect(user).toBeNull()
    })
  })
})

// Component testing example
// __tests__/components/Button.test.tsx
import { render, screen, fireEvent } from '@testing-library/react'
import { Button } from '@/components/ui/Button'

describe('Button Component', () => {
  test('renders with correct text and variant', () => {
    render(<Button variant="primary">Click me</Button>)
    
    const button = screen.getByRole('button', { name: /click me/i })
    expect(button).toBeInTheDocument()
    expect(button).toHaveClass('bg-blue-600')
  })

  test('shows loading state correctly', () => {
    render(<Button loading>Loading</Button>)
    
    const button = screen.getByRole('button')
    expect(button).toBeDisabled()
    expect(screen.getByRole('button')).toHaveAttribute('disabled')
    expect(screen.getByTestId('loading-spinner')).toBeInTheDocument()
  })

  test('handles click events', () => {
    const handleClick = jest.fn()
    render(<Button onClick={handleClick}>Click me</Button>)
    
    fireEvent.click(screen.getByRole('button'))
    expect(handleClick).toHaveBeenCalledTimes(1)
  })

  test('applies custom className', () => {
    render(<Button className="custom-class">Button</Button>)
    
    expect(screen.getByRole('button')).toHaveClass('custom-class')
  })
})
```

#### Level 2: Integration Testing (20% of tests)

**Scope**: API endpoints, database operations, service integrations
**Tools**: Jest, Supertest, Test Containers
**Target Coverage**: 75%

```typescript
// API integration test example
// __tests__/api/twilio.integration.test.ts
import request from 'supertest'
import { app } from '@/app'
import { createTestDatabase, cleanupTestDatabase } from '@/test-utils/database'
import { createTestUser, getAuthToken } from '@/test-utils/auth'

describe('Twilio API Integration', () => {
  let testDb: any
  let authToken: string
  let testUser: any

  beforeAll(async () => {
    testDb = await createTestDatabase()
    testUser = await createTestUser()
    authToken = await getAuthToken(testUser.id)
  })

  afterAll(async () => {
    await cleanupTestDatabase(testDb)
  })

  describe('GET /api/v2/twilio/numbers', () => {
    test('should return user phone numbers', async () => {
      // Seed test data
      await testDb.phoneNumbers.create({
        userId: testUser.id,
        phoneNumber: '+1234567890',
        friendlyName: 'Test Number',
        twilioSid: 'PN123456789'
      })

      const response = await request(app)
        .get('/api/v2/twilio/numbers')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200)

      expect(response.body.success).toBe(true)
      expect(response.body.data).toHaveLength(1)
      expect(response.body.data[0]).toMatchObject({
        phoneNumber: '+1234567890',
        friendlyName: 'Test Number'
      })
    })

    test('should return empty array for user with no numbers', async () => {
      const newUser = await createTestUser()
      const newAuthToken = await getAuthToken(newUser.id)

      const response = await request(app)
        .get('/api/v2/twilio/numbers')
        .set('Authorization', `Bearer ${newAuthToken}`)
        .expect(200)

      expect(response.body.success).toBe(true)
      expect(response.body.data).toHaveLength(0)
    })

    test('should return 401 without authentication', async () => {
      await request(app)
        .get('/api/v2/twilio/numbers')
        .expect(401)
    })
  })

  describe('POST /api/v2/twilio/numbers', () => {
    test('should create new phone number', async () => {
      const numberData = {
        areaCode: '555',
        friendlyName: 'New Test Number'
      }

      const response = await request(app)
        .post('/api/v2/twilio/numbers')
        .set('Authorization', `Bearer ${authToken}`)
        .send(numberData)
        .expect(201)

      expect(response.body.success).toBe(true)
      expect(response.body.data).toMatchObject({
        friendlyName: 'New Test Number'
      })
    })

    test('should validate required fields', async () => {
      const response = await request(app)
        .post('/api/v2/twilio/numbers')
        .set('Authorization', `Bearer ${authToken}`)
        .send({})
        .expect(400)

      expect(response.body.success).toBe(false)
      expect(response.body.error).toBe('Validation failed')
    })
  })
})

// Database integration test
// __tests__/database/user-profiles.integration.test.ts
import { createClient } from '@supabase/supabase-js'
import { Database } from '@/types/supabase'

describe('User Profiles Database Integration', () => {
  let supabase: ReturnType<typeof createClient<Database>>

  beforeAll(() => {
    supabase = createClient(
      process.env.SUPABASE_TEST_URL!,
      process.env.SUPABASE_TEST_ANON_KEY!
    )
  })

  test('should create and retrieve user profile', async () => {
    const profileData = {
      id: '123e4567-e89b-12d3-a456-426614174000',
      email: '<EMAIL>',
      full_name: 'Test User',
      company_name: 'Test Company'
    }

    // Create profile
    const { data: createdProfile, error: createError } = await supabase
      .from('user_profiles')
      .insert(profileData)
      .select()
      .single()

    expect(createError).toBeNull()
    expect(createdProfile).toMatchObject(profileData)

    // Retrieve profile
    const { data: retrievedProfile, error: retrieveError } = await supabase
      .from('user_profiles')
      .select('*')
      .eq('id', profileData.id)
      .single()

    expect(retrieveError).toBeNull()
    expect(retrievedProfile).toMatchObject(profileData)
  })

  test('should enforce unique email constraint', async () => {
    const email = '<EMAIL>'
    
    // Create first profile
    await supabase.from('user_profiles').insert({
      id: '123e4567-e89b-12d3-a456-426614174001',
      email,
      full_name: 'User One'
    })

    // Attempt to create duplicate
    const { error } = await supabase.from('user_profiles').insert({
      id: '123e4567-e89b-12d3-a456-426614174002',
      email,
      full_name: 'User Two'
    })

    expect(error).not.toBeNull()
    expect(error?.code).toBe('23505') // Unique constraint violation
  })
})
```

#### Level 3: End-to-End Testing (10% of tests)

**Scope**: Complete user workflows and critical business processes
**Tools**: Playwright, Cypress
**Target Coverage**: 100% of critical user journeys

```typescript
// E2E test example with Playwright
// __tests__/e2e/user-authentication.spec.ts
import { test, expect } from '@playwright/test'

test.describe('User Authentication Flow', () => {
  test('should complete Google OAuth sign-in flow', async ({ page }) => {
    // Navigate to sign-in page
    await page.goto('/auth/signin')
    
    // Verify sign-in page elements
    await expect(page.getByRole('heading', { name: 'Sign in to CallSaver' })).toBeVisible()
    await expect(page.getByRole('button', { name: 'Sign in with Google' })).toBeVisible()
    
    // Mock Google OAuth response
    await page.route('**/auth/callback*', async route => {
      await route.fulfill({
        status: 302,
        headers: {
          'Location': '/dashboard'
        }
      })
    })
    
    // Click Google sign-in button
    await page.getByRole('button', { name: 'Sign in with Google' }).click()
    
    // Verify redirect to dashboard
    await expect(page).toHaveURL('/dashboard')
    await expect(page.getByRole('heading', { name: 'Dashboard' })).toBeVisible()
  })

  test('should handle authentication errors gracefully', async ({ page }) => {
    await page.goto('/auth/signin')
    
    // Mock OAuth error
    await page.route('**/auth/callback*', async route => {
      await route.fulfill({
        status: 400,
        json: { error: 'OAuth authentication failed' }
      })
    })
    
    await page.getByRole('button', { name: 'Sign in with Google' }).click()
    
    // Verify error message display
    await expect(page.getByText('Authentication failed')).toBeVisible()
    await expect(page.getByRole('button', { name: 'Try again' })).toBeVisible()
  })
})

// Critical user journey test
// __tests__/e2e/phone-number-management.spec.ts
test.describe('Phone Number Management', () => {
  test.beforeEach(async ({ page }) => {
    // Set up authenticated user
    await page.goto('/auth/signin')
    await page.getByRole('button', { name: 'Sign in with Google' }).click()
    await expect(page).toHaveURL('/dashboard')
  })

  test('should purchase and configure phone number', async ({ page }) => {
    // Navigate to numbers page
    await page.getByRole('link', { name: 'Phone Numbers' }).click()
    await expect(page).toHaveURL('/dashboard/numbers')
    
    // Click purchase button
    await page.getByRole('button', { name: 'Purchase Number' }).click()
    
    // Fill purchase form
    await page.getByLabel('Area Code').fill('555')
    await page.getByLabel('Friendly Name').fill('Test Business Line')
    
    // Submit form
    await page.getByRole('button', { name: 'Purchase' }).click()
    
    // Verify success message
    await expect(page.getByText('Phone number purchased successfully')).toBeVisible()
    
    // Verify number appears in list
    await expect(page.getByText('Test Business Line')).toBeVisible()
    await expect(page.getByText('+1555')).toBeVisible()
    
    // Configure AI assistant
    await page.getByRole('button', { name: 'Configure AI' }).click()
    await page.getByLabel('Assistant Name').fill('Business Assistant')
    await page.getByLabel('Voice').selectOption('professional-female')
    await page.getByLabel('System Prompt').fill('You are a helpful business assistant.')
    
    await page.getByRole('button', { name: 'Save Configuration' }).click()
    
    // Verify configuration saved
    await expect(page.getByText('AI assistant configured successfully')).toBeVisible()
  })

  test('should handle call logs and analytics', async ({ page }) => {
    await page.goto('/dashboard/numbers')
    
    // Select a phone number
    await page.getByText('Test Business Line').click()
    
    // Navigate to call logs
    await page.getByRole('tab', { name: 'Call Logs' }).click()
    
    // Verify call logs table
    await expect(page.getByRole('table')).toBeVisible()
    await expect(page.getByRole('columnheader', { name: 'Date' })).toBeVisible()
    await expect(page.getByRole('columnheader', { name: 'From' })).toBeVisible()
    await expect(page.getByRole('columnheader', { name: 'Duration' })).toBeVisible()
    
    // Test filtering
    await page.getByLabel('Filter by date').fill('2024-01-01')
    await page.getByRole('button', { name: 'Apply Filter' }).click()
    
    // Verify filtered results
    await expect(page.getByText('Showing calls from 2024-01-01')).toBeVisible()
  })
})
```

### Performance Testing

#### Load Testing Strategy
```typescript
// Performance test with Artillery
// performance/load-test.yml
config:
  target: 'http://localhost:3001'
  phases:
    - duration: 60
      arrivalRate: 10
      name: "Warm up"
    - duration: 300
      arrivalRate: 50
      name: "Sustained load"
    - duration: 120
      arrivalRate: 100
      name: "Peak load"
  variables:
    authToken: "{{ $randomString() }}"

scenarios:
  - name: "API Load Test"
    weight: 70
    flow:
      - post:
          url: "/api/v2/auth/signin"
          json:
            email: "<EMAIL>"
            password: "password123"
          capture:
            - json: "$.token"
              as: "authToken"
      - get:
          url: "/api/v2/twilio/numbers"
          headers:
            Authorization: "Bearer {{ authToken }}"
      - get:
          url: "/api/v2/call-logs"
          headers:
            Authorization: "Bearer {{ authToken }}"

  - name: "Frontend Load Test"
    weight: 30
    flow:
      - get:
          url: "/"
      - get:
          url: "/dashboard"
      - get:
          url: "/dashboard/numbers"

# Performance monitoring
// utils/performance-monitor.ts
export class PerformanceMonitor {
  static trackPageLoad(pageName: string) {
    if (typeof window !== 'undefined' && window.performance) {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
      const loadTime = navigation.loadEventEnd - navigation.fetchStart
      
      // Send to analytics
      analytics.track('Page Load Performance', {
        page: pageName,
        loadTime,
        domContentLoaded: navigation.domContentLoadedEventEnd - navigation.fetchStart,
        firstContentfulPaint: this.getFirstContentfulPaint()
      })
    }
  }

  static trackAPICall(endpoint: string, startTime: number) {
    const duration = Date.now() - startTime
    
    if (duration > 1000) {
      analytics.track('Slow API Call', {
        endpoint,
        duration,
        timestamp: new Date().toISOString()
      })
    }
  }

  private static getFirstContentfulPaint(): number {
    const paintEntries = performance.getEntriesByType('paint')
    const fcpEntry = paintEntries.find(entry => entry.name === 'first-contentful-paint')
    return fcpEntry ? fcpEntry.startTime : 0
  }
}
```

### Security Testing

#### Automated Security Testing
```typescript
// Security test suite
// __tests__/security/api-security.test.ts
import request from 'supertest'
import { app } from '@/app'

describe('API Security Tests', () => {
  describe('Authentication Security', () => {
    test('should reject requests without authentication', async () => {
      const protectedEndpoints = [
        '/api/v2/twilio/numbers',
        '/api/v2/users/profile',
        '/api/v2/call-logs'
      ]

      for (const endpoint of protectedEndpoints) {
        const response = await request(app).get(endpoint)
        expect(response.status).toBe(401)
      }
    })

    test('should reject invalid JWT tokens', async () => {
      const response = await request(app)
        .get('/api/v2/twilio/numbers')
        .set('Authorization', 'Bearer invalid-token')
        .expect(401)

      expect(response.body.error).toBe('Invalid or expired token')
    })

    test('should reject expired JWT tokens', async () => {
      const expiredToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyLCJleHAiOjE1MTYyMzkwMjJ9.invalid'
      
      const response = await request(app)
        .get('/api/v2/twilio/numbers')
        .set('Authorization', `Bearer ${expiredToken}`)
        .expect(401)
    })
  })

  describe('Input Validation Security', () => {
    test('should prevent SQL injection attempts', async () => {
      const maliciousInputs = [
        "'; DROP TABLE users; --",
        "1' OR '1'='1",
        "admin'/*",
        "' UNION SELECT * FROM users --"
      ]

      for (const input of maliciousInputs) {
        const response = await request(app)
          .post('/api/v2/twilio/numbers')
          .set('Authorization', 'Bearer valid-token')
          .send({ friendlyName: input })

        expect(response.status).toBe(400)
        expect(response.body.error).toBe('Validation failed')
      }
    })

    test('should prevent XSS attacks', async () => {
      const xssPayloads = [
        '<script>alert("xss")</script>',
        'javascript:alert("xss")',
        '<img src="x" onerror="alert(1)">',
        '"><script>alert("xss")</script>'
      ]

      for (const payload of xssPayloads) {
        const response = await request(app)
          .post('/api/v2/users/profile')
          .set('Authorization', 'Bearer valid-token')
          .send({ fullName: payload })

        expect(response.status).toBe(400)
      }
    })
  })

  describe('Rate Limiting', () => {
    test('should enforce rate limits', async () => {
      const requests = Array(101).fill(null).map(() =>
        request(app)
          .get('/api/v2/health')
          .set('X-Forwarded-For', '***********')
      )

      const responses = await Promise.all(requests)
      const rateLimitedResponses = responses.filter(r => r.status === 429)
      
      expect(rateLimitedResponses.length).toBeGreaterThan(0)
    })
  })
})
```

### Test Data Management

#### Test Data Factory
```typescript
// test-utils/factories.ts
import { faker } from '@faker-js/faker'

export class TestDataFactory {
  static createUser(overrides: Partial<User> = {}): User {
    return {
      id: faker.string.uuid(),
      email: faker.internet.email(),
      fullName: faker.person.fullName(),
      companyName: faker.company.name(),
      createdAt: faker.date.past(),
      updatedAt: faker.date.recent(),
      ...overrides
    }
  }

  static createPhoneNumber(overrides: Partial<PhoneNumber> = {}): PhoneNumber {
    return {
      id: faker.string.uuid(),
      phoneNumber: faker.phone.number('+1##########'),
      friendlyName: faker.company.name() + ' Line',
      twilioSid: 'PN' + faker.string.alphanumeric(32),
      capabilities: {
        voice: true,
        sms: true,
        mms: faker.datatype.boolean()
      },
      status: 'active',
      createdAt: faker.date.past(),
      updatedAt: faker.date.recent(),
      ...overrides
    }
  }

  static createCallLog(overrides: Partial<CallLog> = {}): CallLog {
    return {
      id: faker.string.uuid(),
      twilioCallSid: 'CA' + faker.string.alphanumeric(32),
      fromNumber: faker.phone.number('+1##########'),
      toNumber: faker.phone.number('+1##########'),
      direction: faker.helpers.arrayElement(['inbound', 'outbound']),
      status: faker.helpers.arrayElement(['completed', 'busy', 'no-answer']),
      duration: faker.number.int({ min: 0, max: 3600 }),
      recordingUrl: faker.internet.url(),
      transcription: faker.lorem.paragraph(),
      sentimentScore: faker.number.float({ min: -1, max: 1, precision: 0.01 }),
      createdAt: faker.date.past(),
      ...overrides
    }
  }
}
```

### Continuous Integration Pipeline

#### GitHub Actions Workflow
```yaml
# .github/workflows/test.yml
name: Test Suite

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  unit-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'
      
      - run: npm ci
      - run: npm run test:unit
      - run: npm run test:coverage
      
      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v3

  integration-tests:
    runs-on: ubuntu-latest
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
    
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'
      
      - run: npm ci
      - run: npm run test:integration
        env:
          DATABASE_URL: postgresql://postgres:postgres@localhost:5432/test

  e2e-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'
      
      - run: npm ci
      - run: npx playwright install
      - run: npm run build
      - run: npm run test:e2e
      
      - uses: actions/upload-artifact@v3
        if: failure()
        with:
          name: playwright-report
          path: playwright-report/

  security-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - run: npm audit --audit-level moderate
      - run: npx audit-ci --moderate
```

### Test Reporting and Metrics

#### Test Dashboard
```typescript
// Test metrics collection
interface TestMetrics {
  totalTests: number
  passedTests: number
  failedTests: number
  skippedTests: number
  coverage: {
    lines: number
    functions: number
    branches: number
    statements: number
  }
  performance: {
    averageTestTime: number
    slowestTests: Array<{ name: string; duration: number }>
  }
  trends: {
    passRate: number[]
    coverageHistory: number[]
  }
}

export class TestReporter {
  static generateReport(results: TestResults): TestMetrics {
    return {
      totalTests: results.numTotalTests,
      passedTests: results.numPassedTests,
      failedTests: results.numFailedTests,
      skippedTests: results.numPendingTests,
      coverage: results.coverageMap.getCoverageSummary(),
      performance: this.calculatePerformanceMetrics(results),
      trends: this.getTrends()
    }
  }

  static async publishReport(metrics: TestMetrics) {
    // Send to monitoring dashboard
    await fetch('/api/test-metrics', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(metrics)
    })
  }
}
```

### Success Criteria

#### Quality Gates
- **Unit Test Coverage**: ≥85%
- **Integration Test Coverage**: ≥75%
- **E2E Test Coverage**: 100% of critical user journeys
- **Performance Tests**: All tests pass within SLA thresholds
- **Security Tests**: Zero critical/high vulnerabilities
- **Test Execution Time**: <10 minutes for full suite

#### Continuous Monitoring
- **Test Success Rate**: ≥95%
- **Test Stability**: <5% flaky test rate
- **Defect Escape Rate**: <2% to production
- **Mean Time to Detection**: <2 hours
- **Mean Time to Resolution**: <4 hours

This comprehensive testing strategy ensures the CallSaver.app modernization maintains high quality standards while enabling rapid, confident deployment of new features and improvements.
