"use client";

import { motion } from 'framer-motion';
import { useState, useEffect, memo } from 'react';

// Define icons outside component to prevent recreation on every render
const DEFAULT_ICONS = [
  // Communication & Business Icons
  '✉️', '📱', '💬', '🔔', '📞', '🗓️', '📊', '💼',
  '📧', '📨', '📩', '💌', '📮', '📬', '📭', '📯',
  '☎️', '📟', '📠', '📺', '📻', '🎙️', '🎧', '🔊',
  '💻', '🖥️', '⌨️', '🖱️', '🖨️', '💾', '💿', '📀',
  '📈', '📉', '📋', '📌', '📍', '🎯', '💡', '🔍',
  '⚡', '🌟', '✨', '💫', '🔥', '💎', '🎨', '🚀',
  // Geometric shapes for variety
  '🔷', '🔶', '🔸', '🔹', '🟦', '🟧', '🟨', '🟩'
];

/**
 * FallingIcons component - Enhanced with better visibility and performance
 * Creates a background animation of falling icons/particles
 */
function FallingIcons({
  count = 12,
  icons = DEFAULT_ICONS
}) {
  const [particles, setParticles] = useState([]);
  const [isClient, setIsClient] = useState(false);

  // Generate particles only once on mount
  useEffect(() => {
    setIsClient(true);

    // Enhanced particle generation with better visibility
    const newParticles = [];
    // Calculate responsive count inline to avoid dependency issues
    const width = typeof window !== 'undefined' ? window.innerWidth : 1024;
    let particleCount = count;
    if (width < 640) particleCount = Math.min(count * 0.6, 8); // Mobile: fewer particles
    else if (width < 1024) particleCount = Math.min(count * 0.8, 12); // Tablet: moderate particles
    else particleCount = Math.min(count, 16); // Desktop: full count, max 16 for performance

    for (let i = 0; i < particleCount; i++) {
      newParticles.push({
        id: i,
        x: Math.random() * 100,
        y: -10 - Math.random() * 100,
        size: 24 + Math.random() * 16, // Increased base size for better visibility: 24-40px
        duration: 12 + Math.random() * 8, // Slower for smoother animation: 12-20s
        delay: Math.random() * 8,
        rotate: Math.random() * 360,
        icon: icons[Math.floor(Math.random() * icons.length)],
        opacity: 0.4 + Math.random() * 0.4, // Better visibility: 0.4-0.8
        glowIntensity: 0.2 + Math.random() * 0.3, // Enhanced glow effect
        drift: (Math.random() - 0.5) * 40, // More natural horizontal drift
      });
    }

    setParticles(newParticles);
  }, []); // Empty dependency array - only run once on mount

  // Performance monitoring - reduce particles on low-end devices (one-time check)
  useEffect(() => {
    if (typeof window === 'undefined' || !isClient || particles.length === 0) return;

    // One-time performance check after particles are set
    const checkPerformance = () => {
      if (navigator.deviceMemory && navigator.deviceMemory < 4) {
        // Device has less than 4GB RAM, reduce particles
        const reducedCount = Math.max(4, Math.floor(particles.length * 0.5));
        if (particles.length > reducedCount) {
          setParticles(prev => prev.slice(0, reducedCount));
        }
      }
    };

    // Run check after a short delay to ensure particles are set
    const timeoutId = setTimeout(checkPerformance, 100);
    return () => clearTimeout(timeoutId);
  }, [isClient, particles.length]); // Include particles.length dependency

  // Don't render anything during SSR or if no particles
  if (!isClient || particles.length === 0) return null;

  return (
    <div className="fixed inset-0 pointer-events-none overflow-hidden z-0">
      {particles.map((particle) => (
        <motion.div
          key={particle.id}
          className="falling-icon absolute select-none will-change-transform"
          style={{
            left: `${particle.x}%`,
            top: `${particle.y}%`,
            fontSize: `${particle.size}px`,
            color: `rgba(255, 255, 255, ${particle.opacity})`,
            textShadow: `0 0 ${particle.glowIntensity * 20}px rgba(147, 51, 234, ${particle.glowIntensity}), 0 0 ${particle.glowIntensity * 40}px rgba(219, 39, 119, ${particle.glowIntensity * 0.5})`,
            filter: `drop-shadow(0 0 ${particle.glowIntensity * 10}px rgba(147, 51, 234, 0.3))`,
            zIndex: 1,
          }}
          initial={{
            y: particle.y,
            x: particle.x,
            rotate: particle.rotate,
            opacity: 0,
            scale: 0.6
          }}
          animate={{
            y: '120vh',
            x: particle.x + particle.drift,
            rotate: particle.rotate + 180, // Reduced rotation for smoother animation
            opacity: [0, particle.opacity * 0.3, particle.opacity, particle.opacity * 0.8, 0],
            scale: [0.6, 0.9, 1, 0.9, 0.6]
          }}
          transition={{
            duration: particle.duration,
            delay: particle.delay,
            repeat: Infinity,
            ease: 'easeInOut', // Smoother easing
            opacity: {
              ease: 'easeInOut',
              times: [0, 0.1, 0.5, 0.9, 1] // More controlled opacity transitions
            },
            scale: {
              ease: 'easeInOut',
              times: [0, 0.2, 0.5, 0.8, 1]
            }
          }}
        >
          {particle.icon}
        </motion.div>
      ))}
    </div>
  );
}

// Memoize the component to prevent unnecessary re-renders
export default memo(FallingIcons);
