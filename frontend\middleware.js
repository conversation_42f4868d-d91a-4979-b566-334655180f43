import { createServerClient } from '@supabase/ssr';
import { NextResponse } from 'next/server';

// Middleware to handle Supabase auth and security headers
export async function middleware(req) {
  try {
    // Create a new response object
    const res = NextResponse.next();

    // Generate a unique nonce for this request using Web Crypto API
    const nonce = Array.from(crypto.getRandomValues(new Uint8Array(16)))
      .map(b => b.toString(16).padStart(2, '0'))
      .join('');

    // Add the nonce to the request headers so it can be accessed by components
    res.headers.set('x-nonce', nonce);

    // Add security headers with nonce-based CSP
    const cspDirectives = [
      "default-src 'self'",
      `script-src 'self' 'unsafe-eval' 'nonce-${nonce}' https://*.stripe.com https://m.stripe.network https://js.stripe.com https://q.stripe.com chrome-extension: moz-extension:`,
      "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com",
      "font-src 'self' https://fonts.gstatic.com",
      "img-src 'self' data: blob: https://*.stripe.com https://tools.applemediaservices.com https://play.google.com https://randomuser.me https://upload.wikimedia.org https://cdn.worldvectorlogo.com https://placehold.co",
      "connect-src 'self' https://*.stripe.com https://m.stripe.network https://api.stripe.com https://q.stripe.com wss: ws: https://*.supabase.co wss://*.supabase.co",
      "frame-src 'self' https://*.stripe.com https://js.stripe.com",
      "object-src 'none'",
      "base-uri 'self'",
      "form-action 'self' https://*.stripe.com",
      "frame-ancestors 'self'",
      "upgrade-insecure-requests"
    ];

    const securityHeaders = {
      'X-DNS-Prefetch-Control': 'on',
      'X-XSS-Protection': '1; mode=block',
      'X-Frame-Options': 'SAMEORIGIN',
      'X-Content-Type-Options': 'nosniff',
      'Referrer-Policy': 'strict-origin-when-cross-origin',
      'Permissions-Policy': 'camera=(), microphone=(), geolocation=(self), interest-cohort=()',
      'Content-Security-Policy': cspDirectives.join('; '),
    };

    // Add security headers to the response
    Object.entries(securityHeaders).forEach(([key, value]) => {
      res.headers.set(key, value);
    });

    // Create a Supabase client specifically for the middleware
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
      {
        cookies: {
          get: (name) => {
            const cookie = req.cookies.get(name);
            return cookie?.value;
          },
          set: (name, value, options) => {
            // Ensure proper cookie options, especially for secure environments
            const isProduction = process.env.NODE_ENV === 'production';
            
            res.cookies.set({
              name,
              value,
              ...options,
              secure: isProduction,
              path: options?.path || '/',
              sameSite: options?.sameSite || 'lax',
              maxAge: options?.maxAge || 60 * 60 * 24 * 7, // Default to 7 days if not specified
            });
          },
          remove: (name, options) => {
            res.cookies.set({
              name,
              value: '',
              maxAge: -1,
              ...options,
              path: options?.path || '/',
            });
          },
        },
      }
    );

    // Check if this is a just_signed_in redirection
    const isPostSignIn = req.nextUrl.searchParams.get('just_signed_in') === 'true';
    
    // Handle session refresh for post-sign-in requests to ensure session is properly established
    if (isPostSignIn) {
      try {
        await supabase.auth.refreshSession();
        console.log('Refreshed session after sign in');
      } catch (refreshError) {
        console.error('Error refreshing session after sign in:', refreshError);
      }
    } else {
      // For regular requests, try to refresh the session if needed
      try {
        await supabase.auth.refreshSession();
      } catch (refreshError) {
        console.error('Regular session refresh error:', refreshError);
      }
    }

    // Now get the session data (which might have been updated by refreshSession)
    const { data, error } = await supabase.auth.getSession();
    
    if (error) {
      console.error('Error getting session in middleware:', error);
    }

    // Debug log for auth issues
    if (req.nextUrl.pathname.startsWith('/dashboard')) {
      console.log('Dashboard access attempt, session exists:', !!data?.session);
    }

    // Check if the requested path is a protected route (dashboard)
    if (req.nextUrl.pathname.startsWith('/dashboard')) {
      // If user is not authenticated, redirect to sign-in page
      if (!data?.session) {
        console.log('Unauthorized dashboard access attempt - redirecting to sign-in');
        return NextResponse.redirect(new URL('/signin', req.url));
      }
    }

    // For all other routes, return the next response with cookies
    return res;
  } catch (error) {
    // Log any errors but don't block the request
    console.error('Error in auth middleware:', error);
    // Return the next response
    return NextResponse.next();
  }
}

// Specify which routes the middleware should run on
export const config = {
  matcher: ['/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)'],
};