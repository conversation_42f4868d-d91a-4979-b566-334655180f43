# Backend Refactoring Plan
## CallSaver.app - API Modernization and Architecture Enhancement

### Executive Summary

This document outlines the comprehensive backend refactoring strategy for CallSaver.app, focusing on API modernization, database optimization, microservices architecture preparation, and enhanced security while maintaining backward compatibility and zero-downtime deployment.

### Current Backend Architecture Analysis

#### Technology Stack Assessment
```json
{
  "current": {
    "runtime": "Node.js 18.x",
    "framework": "Express.js 4.x",
    "databases": ["PostgreSQL + Prisma", "MongoDB + Mongoose"],
    "authentication": "NextAuth.js",
    "api_style": "REST",
    "deployment": "Traditional server",
    "monitoring": "Basic logging"
  },
  "target": {
    "runtime": "Node.js 20.x LTS",
    "framework": "Express.js 5.x + Fastify (selective)",
    "databases": ["Supabase PostgreSQL", "MongoDB Atlas"],
    "authentication": "Supabase Auth + JWT",
    "api_style": "REST + GraphQL (selective)",
    "deployment": "Containerized + Serverless hybrid",
    "monitoring": "Comprehensive observability"
  }
}
```

#### Current API Structure
```typescript
// Current Express.js structure
src/
├── routes/
│   ├── auth.js          // Authentication routes
│   ├── twilio.js        // Twilio integration
│   ├── users.js         // User management
│   └── webhooks.js      // Webhook handlers
├── middleware/
│   ├── auth.js          // Auth middleware
│   └── validation.js    // Input validation
├── models/
│   ├── User.js          // Mongoose models
│   └── Call.js          // Call records
├── services/
│   ├── twilioService.js // Twilio operations
│   └── aiService.js     // AI integrations
└── utils/
    ├── database.js      // DB connections
    └── logger.js        // Logging utility
```

### Phase 1: API Architecture Modernization (Week 1-3)

#### 1.1 Express.js 5.x Migration
```typescript
// New Express 5.x setup with modern patterns
// src/app.ts
import express from 'express'
import cors from 'cors'
import helmet from 'helmet'
import rateLimit from 'express-rate-limit'
import { createServer } from 'http'
import { Server } from 'socket.io'

const app = express()
const server = createServer(app)
const io = new Server(server, {
  cors: {
    origin: process.env.FRONTEND_URL,
    methods: ['GET', 'POST']
  }
})

// Security middleware
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      scriptSrc: ["'self'", "'unsafe-inline'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      imgSrc: ["'self'", "data:", "https:"],
    }
  }
}))

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: 'Too many requests from this IP',
  standardHeaders: true,
  legacyHeaders: false
})

app.use('/api/', limiter)

// CORS configuration
app.use(cors({
  origin: process.env.FRONTEND_URL,
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'],
  allowedHeaders: ['Content-Type', 'Authorization']
}))

// Body parsing
app.use(express.json({ limit: '10mb' }))
app.use(express.urlencoded({ extended: true, limit: '10mb' }))

export { app, server, io }
```

#### 1.2 Modern Route Architecture
```typescript
// src/routes/v2/index.ts - New API version
import { Router } from 'express'
import { authRoutes } from './auth'
import { twilioRoutes } from './twilio'
import { userRoutes } from './users'
import { webhookRoutes } from './webhooks'
import { aiRoutes } from './ai'

const v2Router = Router()

// API versioning
v2Router.use('/auth', authRoutes)
v2Router.use('/twilio', twilioRoutes)
v2Router.use('/users', userRoutes)
v2Router.use('/webhooks', webhookRoutes)
v2Router.use('/ai', aiRoutes)

// Health check endpoint
v2Router.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    version: process.env.API_VERSION || '2.0.0'
  })
})

export { v2Router }

// src/routes/v2/twilio.ts - Enhanced Twilio routes
import { Router } from 'express'
import { z } from 'zod'
import { validateRequest } from '@/middleware/validation'
import { requireAuth } from '@/middleware/auth'
import { TwilioService } from '@/services/TwilioService'
import { asyncHandler } from '@/utils/asyncHandler'

const router = Router()
const twilioService = new TwilioService()

// Validation schemas
const createNumberSchema = z.object({
  areaCode: z.string().length(3),
  friendlyName: z.string().min(1).max(64),
  voiceUrl: z.string().url().optional(),
  smsUrl: z.string().url().optional()
})

const updateNumberSchema = z.object({
  friendlyName: z.string().min(1).max(64).optional(),
  voiceUrl: z.string().url().optional(),
  smsUrl: z.string().url().optional()
})

// Routes
router.get('/numbers', 
  requireAuth,
  asyncHandler(async (req, res) => {
    const numbers = await twilioService.getNumbers(req.user.id)
    res.json({
      success: true,
      data: numbers,
      meta: {
        total: numbers.length,
        timestamp: new Date().toISOString()
      }
    })
  })
)

router.post('/numbers',
  requireAuth,
  validateRequest(createNumberSchema),
  asyncHandler(async (req, res) => {
    const number = await twilioService.createNumber(req.user.id, req.body)
    res.status(201).json({
      success: true,
      data: number,
      message: 'Phone number created successfully'
    })
  })
)

router.put('/numbers/:numberId',
  requireAuth,
  validateRequest(updateNumberSchema),
  asyncHandler(async (req, res) => {
    const number = await twilioService.updateNumber(
      req.user.id,
      req.params.numberId,
      req.body
    )
    res.json({
      success: true,
      data: number,
      message: 'Phone number updated successfully'
    })
  })
)

router.delete('/numbers/:numberId',
  requireAuth,
  asyncHandler(async (req, res) => {
    await twilioService.deleteNumber(req.user.id, req.params.numberId)
    res.json({
      success: true,
      message: 'Phone number deleted successfully'
    })
  })
)

export { router as twilioRoutes }
```

#### 1.3 Enhanced Middleware System
```typescript
// src/middleware/auth.ts - Modern authentication middleware
import { Request, Response, NextFunction } from 'express'
import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'

interface AuthenticatedRequest extends Request {
  user: {
    id: string
    email: string
    role: string
  }
}

export const requireAuth = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
) => {
  try {
    const authHeader = req.headers.authorization
    if (!authHeader?.startsWith('Bearer ')) {
      return res.status(401).json({
        success: false,
        error: 'Missing or invalid authorization header'
      })
    }

    const token = authHeader.substring(7)
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      { cookies }
    )

    const { data: { user }, error } = await supabase.auth.getUser(token)
    
    if (error || !user) {
      return res.status(401).json({
        success: false,
        error: 'Invalid or expired token'
      })
    }

    req.user = {
      id: user.id,
      email: user.email!,
      role: user.user_metadata?.role || 'user'
    }

    next()
  } catch (error) {
    console.error('Auth middleware error:', error)
    res.status(500).json({
      success: false,
      error: 'Internal authentication error'
    })
  }
}

// src/middleware/validation.ts - Enhanced validation
import { z } from 'zod'
import { Request, Response, NextFunction } from 'express'

export const validateRequest = (schema: z.ZodSchema) => {
  return (req: Request, res: Response, next: NextFunction) => {
    try {
      const result = schema.safeParse(req.body)
      
      if (!result.success) {
        return res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: result.error.errors.map(err => ({
            field: err.path.join('.'),
            message: err.message
          }))
        })
      }

      req.body = result.data
      next()
    } catch (error) {
      res.status(500).json({
        success: false,
        error: 'Validation error'
      })
    }
  }
}

// src/middleware/errorHandler.ts - Centralized error handling
export const errorHandler = (
  error: Error,
  req: Request,
  res: Response,
  next: NextFunction
) => {
  console.error('API Error:', {
    message: error.message,
    stack: error.stack,
    url: req.url,
    method: req.method,
    timestamp: new Date().toISOString()
  })

  // Operational errors
  if (error.name === 'ValidationError') {
    return res.status(400).json({
      success: false,
      error: 'Validation failed',
      message: error.message
    })
  }

  if (error.name === 'UnauthorizedError') {
    return res.status(401).json({
      success: false,
      error: 'Unauthorized',
      message: 'Authentication required'
    })
  }

  if (error.name === 'ForbiddenError') {
    return res.status(403).json({
      success: false,
      error: 'Forbidden',
      message: 'Insufficient permissions'
    })
  }

  // Default server error
  res.status(500).json({
    success: false,
    error: 'Internal server error',
    message: process.env.NODE_ENV === 'development' ? error.message : 'Something went wrong'
  })
}
```

### Phase 2: Service Layer Modernization (Week 4-6)

#### 2.1 Enhanced Twilio Service
```typescript
// src/services/TwilioService.ts - Modern service architecture
import twilio from 'twilio'
import { z } from 'zod'
import { createClient } from '@supabase/supabase-js'
import { Logger } from '@/utils/Logger'
import { CacheService } from '@/services/CacheService'

interface TwilioCredentials {
  accountSid: string
  authToken: string
}

interface PhoneNumber {
  id: string
  phoneNumber: string
  friendlyName: string
  capabilities: {
    voice: boolean
    sms: boolean
    mms: boolean
  }
  status: 'active' | 'inactive'
  createdAt: Date
  updatedAt: Date
}

export class TwilioService {
  private supabase = createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!
  )
  private logger = new Logger('TwilioService')
  private cache = new CacheService()

  private async getTwilioClient(userId: string): Promise<twilio.Twilio> {
    const cacheKey = `twilio_credentials:${userId}`
    let credentials = await this.cache.get<TwilioCredentials>(cacheKey)

    if (!credentials) {
      const { data, error } = await this.supabase
        .from('twilio_credentials')
        .select('account_sid, auth_token')
        .eq('user_id', userId)
        .single()

      if (error || !data) {
        throw new Error('Twilio credentials not found')
      }

      credentials = {
        accountSid: data.account_sid,
        authToken: data.auth_token
      }

      await this.cache.set(cacheKey, credentials, 300) // 5 minutes
    }

    return twilio(credentials.accountSid, credentials.authToken)
  }

  async getNumbers(userId: string): Promise<PhoneNumber[]> {
    try {
      const cacheKey = `twilio_numbers:${userId}`
      let numbers = await this.cache.get<PhoneNumber[]>(cacheKey)

      if (!numbers) {
        const client = await this.getTwilioClient(userId)
        const twilioNumbers = await client.incomingPhoneNumbers.list()

        numbers = twilioNumbers.map(num => ({
          id: num.sid,
          phoneNumber: num.phoneNumber,
          friendlyName: num.friendlyName || num.phoneNumber,
          capabilities: {
            voice: num.capabilities.voice,
            sms: num.capabilities.sms,
            mms: num.capabilities.mms
          },
          status: 'active',
          createdAt: new Date(num.dateCreated),
          updatedAt: new Date(num.dateUpdated)
        }))

        await this.cache.set(cacheKey, numbers, 60) // 1 minute
      }

      this.logger.info(`Retrieved ${numbers.length} numbers for user ${userId}`)
      return numbers
    } catch (error) {
      this.logger.error('Failed to get numbers', { userId, error })
      throw new Error('Failed to retrieve phone numbers')
    }
  }

  async createNumber(userId: string, data: {
    areaCode: string
    friendlyName: string
    voiceUrl?: string
    smsUrl?: string
  }): Promise<PhoneNumber> {
    try {
      const client = await this.getTwilioClient(userId)
      
      // Search for available numbers
      const availableNumbers = await client.availablePhoneNumbers('US')
        .local
        .list({
          areaCode: data.areaCode,
          limit: 1
        })

      if (availableNumbers.length === 0) {
        throw new Error(`No available numbers in area code ${data.areaCode}`)
      }

      // Purchase the number
      const purchasedNumber = await client.incomingPhoneNumbers.create({
        phoneNumber: availableNumbers[0].phoneNumber,
        friendlyName: data.friendlyName,
        voiceUrl: data.voiceUrl || process.env.TWILIO_VOICE_WEBHOOK_URL,
        smsUrl: data.smsUrl || process.env.TWILIO_SMS_WEBHOOK_URL
      })

      const number: PhoneNumber = {
        id: purchasedNumber.sid,
        phoneNumber: purchasedNumber.phoneNumber,
        friendlyName: purchasedNumber.friendlyName || purchasedNumber.phoneNumber,
        capabilities: {
          voice: purchasedNumber.capabilities.voice,
          sms: purchasedNumber.capabilities.sms,
          mms: purchasedNumber.capabilities.mms
        },
        status: 'active',
        createdAt: new Date(purchasedNumber.dateCreated),
        updatedAt: new Date(purchasedNumber.dateUpdated)
      }

      // Invalidate cache
      await this.cache.delete(`twilio_numbers:${userId}`)

      this.logger.info('Number created successfully', { userId, numberId: number.id })
      return number
    } catch (error) {
      this.logger.error('Failed to create number', { userId, error })
      throw new Error('Failed to create phone number')
    }
  }

  async updateNumber(userId: string, numberId: string, data: {
    friendlyName?: string
    voiceUrl?: string
    smsUrl?: string
  }): Promise<PhoneNumber> {
    try {
      const client = await this.getTwilioClient(userId)
      
      const updatedNumber = await client.incomingPhoneNumbers(numberId).update({
        friendlyName: data.friendlyName,
        voiceUrl: data.voiceUrl,
        smsUrl: data.smsUrl
      })

      const number: PhoneNumber = {
        id: updatedNumber.sid,
        phoneNumber: updatedNumber.phoneNumber,
        friendlyName: updatedNumber.friendlyName || updatedNumber.phoneNumber,
        capabilities: {
          voice: updatedNumber.capabilities.voice,
          sms: updatedNumber.capabilities.sms,
          mms: updatedNumber.capabilities.mms
        },
        status: 'active',
        createdAt: new Date(updatedNumber.dateCreated),
        updatedAt: new Date(updatedNumber.dateUpdated)
      }

      // Invalidate cache
      await this.cache.delete(`twilio_numbers:${userId}`)

      this.logger.info('Number updated successfully', { userId, numberId })
      return number
    } catch (error) {
      this.logger.error('Failed to update number', { userId, numberId, error })
      throw new Error('Failed to update phone number')
    }
  }

  async deleteNumber(userId: string, numberId: string): Promise<void> {
    try {
      const client = await this.getTwilioClient(userId)
      await client.incomingPhoneNumbers(numberId).remove()

      // Invalidate cache
      await this.cache.delete(`twilio_numbers:${userId}`)

      this.logger.info('Number deleted successfully', { userId, numberId })
    } catch (error) {
      this.logger.error('Failed to delete number', { userId, numberId, error })
      throw new Error('Failed to delete phone number')
    }
  }
}
```

#### 2.2 AI Service Integration
```typescript
// src/services/AIService.ts - ElevenLabs integration
import { ElevenLabsApi } from 'elevenlabs'
import { Logger } from '@/utils/Logger'

export class AIService {
  private elevenlabs: ElevenLabsApi
  private logger = new Logger('AIService')

  constructor() {
    this.elevenlabs = new ElevenLabsApi({
      apiKey: process.env.ELEVENLABS_API_KEY!
    })
  }

  async createConversationalAgent(config: {
    name: string
    voiceId: string
    prompt: string
    webhookUrl: string
  }) {
    try {
      const agent = await this.elevenlabs.conversationalAi.createAgent({
        name: config.name,
        voice_id: config.voiceId,
        system_prompt: config.prompt,
        webhook_url: config.webhookUrl,
        language: 'en'
      })

      this.logger.info('Conversational agent created', { agentId: agent.agent_id })
      return agent
    } catch (error) {
      this.logger.error('Failed to create conversational agent', { error })
      throw new Error('Failed to create AI agent')
    }
  }

  async startConversation(agentId: string, phoneNumber: string) {
    try {
      const conversation = await this.elevenlabs.conversationalAi.startConversation({
        agent_id: agentId,
        phone_number: phoneNumber
      })

      this.logger.info('Conversation started', { 
        agentId, 
        conversationId: conversation.conversation_id 
      })
      return conversation
    } catch (error) {
      this.logger.error('Failed to start conversation', { agentId, error })
      throw new Error('Failed to start AI conversation')
    }
  }
}
```

### Phase 3: Database Optimization (Week 7-8)

#### 3.1 Supabase Migration
```sql
-- Enhanced database schema for Supabase
-- User profiles with comprehensive data
CREATE TABLE public.user_profiles (
  id UUID REFERENCES auth.users(id) PRIMARY KEY,
  email TEXT NOT NULL UNIQUE,
  full_name TEXT,
  avatar_url TEXT,
  company_name TEXT,
  phone_number TEXT,
  subscription_tier TEXT DEFAULT 'free' CHECK (subscription_tier IN ('free', 'pro', 'enterprise')),
  subscription_status TEXT DEFAULT 'active' CHECK (subscription_status IN ('active', 'cancelled', 'past_due')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Twilio credentials with encryption
CREATE TABLE public.twilio_credentials (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) NOT NULL,
  account_sid TEXT NOT NULL,
  auth_token TEXT NOT NULL, -- Should be encrypted at application level
  subaccount_sid TEXT,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, account_sid)
);

-- Phone numbers management
CREATE TABLE public.phone_numbers (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) NOT NULL,
  twilio_sid TEXT NOT NULL UNIQUE,
  phone_number TEXT NOT NULL,
  friendly_name TEXT,
  country_code TEXT DEFAULT 'US',
  capabilities JSONB DEFAULT '{"voice": true, "sms": true, "mms": false}',
  webhook_urls JSONB DEFAULT '{}',
  status TEXT DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'suspended')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Call logs with enhanced tracking
CREATE TABLE public.call_logs (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) NOT NULL,
  phone_number_id UUID REFERENCES public.phone_numbers(id),
  twilio_call_sid TEXT NOT NULL UNIQUE,
  from_number TEXT NOT NULL,
  to_number TEXT NOT NULL,
  direction TEXT NOT NULL CHECK (direction IN ('inbound', 'outbound')),
  status TEXT NOT NULL,
  duration INTEGER DEFAULT 0,
  recording_url TEXT,
  transcription TEXT,
  sentiment_score DECIMAL(3,2),
  ai_summary TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- AI agents configuration
CREATE TABLE public.ai_agents (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) NOT NULL,
  phone_number_id UUID REFERENCES public.phone_numbers(id),
  elevenlabs_agent_id TEXT NOT NULL,
  name TEXT NOT NULL,
  voice_id TEXT NOT NULL,
  system_prompt TEXT NOT NULL,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS on all tables
ALTER TABLE public.user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.twilio_credentials ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.phone_numbers ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.call_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.ai_agents ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY "Users can view own profile" ON public.user_profiles
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON public.user_profiles
  FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can view own Twilio credentials" ON public.twilio_credentials
  FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can manage own phone numbers" ON public.phone_numbers
  FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can view own call logs" ON public.call_logs
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can manage own AI agents" ON public.ai_agents
  FOR ALL USING (auth.uid() = user_id);

-- Indexes for performance
CREATE INDEX idx_phone_numbers_user_id ON public.phone_numbers(user_id);
CREATE INDEX idx_call_logs_user_id ON public.call_logs(user_id);
CREATE INDEX idx_call_logs_phone_number_id ON public.call_logs(phone_number_id);
CREATE INDEX idx_call_logs_created_at ON public.call_logs(created_at DESC);
CREATE INDEX idx_ai_agents_user_id ON public.ai_agents(user_id);
```

### Phase 4: Monitoring and Observability (Week 9-10)

#### 4.1 Comprehensive Logging
```typescript
// src/utils/Logger.ts - Enhanced logging system
import winston from 'winston'
import { Request, Response } from 'express'

export class Logger {
  private logger: winston.Logger

  constructor(service: string) {
    this.logger = winston.createLogger({
      level: process.env.LOG_LEVEL || 'info',
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.errors({ stack: true }),
        winston.format.json()
      ),
      defaultMeta: { service },
      transports: [
        new winston.transports.File({ filename: 'logs/error.log', level: 'error' }),
        new winston.transports.File({ filename: 'logs/combined.log' }),
        new winston.transports.Console({
          format: winston.format.combine(
            winston.format.colorize(),
            winston.format.simple()
          )
        })
      ]
    })
  }

  info(message: string, meta?: any) {
    this.logger.info(message, meta)
  }

  error(message: string, meta?: any) {
    this.logger.error(message, meta)
  }

  warn(message: string, meta?: any) {
    this.logger.warn(message, meta)
  }

  debug(message: string, meta?: any) {
    this.logger.debug(message, meta)
  }
}

// Request logging middleware
export const requestLogger = (req: Request, res: Response, next: Function) => {
  const start = Date.now()
  const logger = new Logger('HTTP')

  res.on('finish', () => {
    const duration = Date.now() - start
    logger.info('HTTP Request', {
      method: req.method,
      url: req.url,
      status: res.statusCode,
      duration,
      userAgent: req.get('User-Agent'),
      ip: req.ip
    })
  })

  next()
}
```

### Testing Strategy

#### API Testing with Jest and Supertest
```typescript
// __tests__/api/twilio.test.ts
import request from 'supertest'
import { app } from '@/app'
import { TwilioService } from '@/services/TwilioService'

jest.mock('@/services/TwilioService')

describe('Twilio API Endpoints', () => {
  const mockTwilioService = TwilioService as jest.MockedClass<typeof TwilioService>

  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('GET /api/v2/twilio/numbers', () => {
    test('should return user phone numbers', async () => {
      const mockNumbers = [
        {
          id: 'PN123',
          phoneNumber: '+1234567890',
          friendlyName: 'Test Number',
          capabilities: { voice: true, sms: true, mms: false },
          status: 'active',
          createdAt: new Date(),
          updatedAt: new Date()
        }
      ]

      mockTwilioService.prototype.getNumbers.mockResolvedValue(mockNumbers)

      const response = await request(app)
        .get('/api/v2/twilio/numbers')
        .set('Authorization', 'Bearer valid-token')
        .expect(200)

      expect(response.body.success).toBe(true)
      expect(response.body.data).toEqual(mockNumbers)
    })

    test('should return 401 without authentication', async () => {
      await request(app)
        .get('/api/v2/twilio/numbers')
        .expect(401)
    })
  })
})
```

### Deployment Strategy

#### Docker Configuration
```dockerfile
# Dockerfile
FROM node:20-alpine AS base
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

FROM node:20-alpine AS build
WORKDIR /app
COPY package*.json ./
RUN npm ci
COPY . .
RUN npm run build

FROM base AS runtime
COPY --from=build /app/dist ./dist
COPY --from=build /app/node_modules ./node_modules
EXPOSE 3001
CMD ["node", "dist/server.js"]
```

### Migration Timeline

| Week | Phase | Focus Area | Deliverables |
|------|-------|------------|--------------|
| 1-3  | API Modernization | Express 5.x, routing, middleware | Modern API architecture |
| 4-6  | Service Layer | Enhanced services, AI integration | Robust service layer |
| 7-8  | Database | Supabase migration, optimization | Optimized data layer |
| 9-10 | Monitoring | Logging, metrics, observability | Production monitoring |
| 11-12| Testing & Deploy | Comprehensive testing, deployment | Production-ready backend |

### Success Metrics

- **API Response Time**: <200ms average
- **Error Rate**: <1% for all endpoints
- **Uptime**: >99.9% availability
- **Security**: Zero critical vulnerabilities
- **Performance**: Handle 1000+ concurrent requests

This backend refactoring plan ensures a modern, scalable, and maintainable API architecture that supports the CallSaver.app modernization goals while maintaining reliability and performance.
