# Proposed Architecture

## Executive Summary

The proposed architecture simplifies the current system by consolidating databases, streamlining authentication, and integrating professional-grade AI services. This design prioritizes maintainability, performance, and scalability while preserving the Apple-like user experience.

## High-Level Architecture Overview

```mermaid
graph TB
    subgraph "Client Layer"
        A[Web Browser]
        B[Mobile Browser/PWA]
    end
    
    subgraph "Frontend (Next.js 15 + React 19)"
        C[Next.js App Router]
        D[React Components]
        E[Zustand State Management]
        F[TanStack Query]
        G[Supabase Auth Client]
    end
    
    subgraph "Backend Services"
        H[Next.js API Routes]
        I[Supabase Edge Functions]
        J[ElevenLabs Webhooks]
    end
    
    subgraph "Database Layer"
        K[(PostgreSQL<br/>New Supabase Project)]
    end
    
    subgraph "AI & Communication Services"
        L[ElevenLabs Conversational AI]
        M[User's Twilio Account]
        N[OpenAI/Claude/Gemini]
    end
    
    subgraph "Supporting Services"
        O[Stripe Payments]
        P[Vercel Analytics]
        Q[Sentry Error Tracking]
    end
    
    A --> C
    B --> C
    C --> D
    D --> E
    D --> F
    D --> G
    F --> H
    H --> K
    G --> K
    H --> L
    L --> M
    L --> N
    J --> H
    H --> O
    C --> P
    C --> Q
```

## Core Architecture Principles

### 1. Simplification
- **Single Database**: PostgreSQL only (eliminate MongoDB)
- **Unified Authentication**: Supabase Auth only (remove NextAuth)
- **Managed AI Services**: ElevenLabs replaces custom Twilio integration
- **Modern State Management**: Zustand + TanStack Query

### 2. Performance First
- **Edge Computing**: Leverage Vercel Edge Network
- **Intelligent Caching**: Multi-layer caching strategy
- **Optimized Bundles**: Code splitting and tree shaking
- **Real-time Updates**: Supabase real-time subscriptions

### 3. Security by Design
- **Row Level Security**: Database-level access control
- **Type Safety**: Full TypeScript implementation
- **Input Validation**: Zod schema validation
- **Secure Headers**: Comprehensive security headers

### 4. Developer Experience
- **Modern Tooling**: Latest Next.js, React, and TypeScript
- **Comprehensive Testing**: Unit, integration, and E2E tests
- **Clear Documentation**: Living documentation
- **Monitoring**: Real-time error tracking and performance monitoring

## Detailed Component Architecture

### Frontend Architecture (Next.js 15 + React 19)

#### Technology Stack
```mermaid
graph TD
    A[Next.js 15] --> B[React 19]
    A --> C[App Router]
    A --> D[Server Components]
    B --> E[Concurrent Features]
    B --> F[Suspense]
    G[Zustand] --> H[Client State]
    I[TanStack Query] --> J[Server State]
    K[Supabase Auth] --> L[Authentication]
    M[TailwindCSS] --> N[Styling]
    O[Framer Motion] --> P[Animations]
```

#### State Management Strategy
```javascript
// Zustand stores for client state
const useAuthStore = create((set) => ({
  user: null,
  setUser: (user) => set({ user }),
  logout: () => set({ user: null })
}));

// TanStack Query for server state
const useAgents = () => {
  return useQuery({
    queryKey: ['agents'],
    queryFn: fetchAgents,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};
```

#### Component Architecture
```
components/
├── ui/                     # Reusable UI components
│   ├── Button.jsx
│   ├── Input.jsx
│   ├── Modal.jsx
│   └── DataTable.jsx
├── auth/                   # Authentication components
│   ├── AuthProvider.jsx
│   ├── LoginForm.jsx
│   └── ProtectedRoute.jsx
├── agents/                 # AI agent management
│   ├── AgentBuilder.jsx
│   ├── VoiceSelector.jsx
│   ├── PromptEditor.jsx
│   └── AgentTesting.jsx
├── dashboard/              # Dashboard components
│   ├── CallsOverview.jsx
│   ├── AnalyticsChart.jsx
│   └── RealtimeMetrics.jsx
└── phone-numbers/          # Phone number management
    ├── NumberImport.jsx
    ├── NumberList.jsx
    └── AgentAssignment.jsx
```

### Database Architecture (Single PostgreSQL)

#### Enhanced Schema Design
```sql
-- Core user management
CREATE TABLE users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email VARCHAR UNIQUE NOT NULL,
  name VARCHAR,
  avatar_url VARCHAR,
  preferences JSONB DEFAULT '{}',
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- AI agents configuration
CREATE TABLE ai_agents (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  elevenlabs_agent_id VARCHAR UNIQUE,
  name VARCHAR NOT NULL,
  prompt TEXT NOT NULL,
  voice_id VARCHAR,
  llm_config JSONB DEFAULT '{}',
  conversation_config JSONB DEFAULT '{}',
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Enhanced phone numbers
CREATE TABLE phone_numbers (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  number VARCHAR UNIQUE NOT NULL,
  friendly_name VARCHAR,
  country_code VARCHAR(3) NOT NULL,
  capabilities JSONB DEFAULT '{"voice": true, "sms": true}',
  twilio_sid VARCHAR,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Comprehensive conversation tracking
CREATE TABLE conversations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  agent_id UUID REFERENCES ai_agents(id),
  phone_number_id UUID REFERENCES phone_numbers(id),
  elevenlabs_conversation_id VARCHAR,
  from_number VARCHAR NOT NULL,
  to_number VARCHAR NOT NULL,
  direction VARCHAR CHECK (direction IN ('inbound', 'outbound')),
  status VARCHAR NOT NULL,
  duration_seconds INTEGER,
  transcript TEXT,
  summary TEXT,
  sentiment VARCHAR,
  ai_analysis JSONB DEFAULT '{}',
  created_at TIMESTAMP DEFAULT NOW(),
  ended_at TIMESTAMP
);
```

#### Row Level Security (RLS) Policies
```sql
-- Enable RLS on all tables
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE ai_agents ENABLE ROW LEVEL SECURITY;
ALTER TABLE phone_numbers ENABLE ROW LEVEL SECURITY;
ALTER TABLE conversations ENABLE ROW LEVEL SECURITY;

-- Users can only access their own data
CREATE POLICY "Users can manage own data" ON users
  FOR ALL USING (auth.uid() = id);

CREATE POLICY "Users can manage own agents" ON ai_agents
  FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can manage own phone numbers" ON phone_numbers
  FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can view own conversations" ON conversations
  FOR SELECT USING (auth.uid() = user_id);
```

### Authentication Architecture (Supabase Auth Only)

#### Simplified Auth Flow
```mermaid
sequenceDiagram
    participant U as User
    participant F as Frontend
    participant S as Supabase Auth
    participant D as Database
    
    U->>F: Login Request
    F->>S: Supabase Auth
    S->>S: OAuth/Email Auth
    S->>D: Create/Update User
    D->>F: User Session
    F->>U: Authenticated
```

#### Auth Implementation
```javascript
// Supabase auth client
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
);

// Auth provider component
export function AuthProvider({ children }) {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Get initial session
    supabase.auth.getSession().then(({ data: { session } }) => {
      setUser(session?.user ?? null);
      setLoading(false);
    });

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      (event, session) => {
        setUser(session?.user ?? null);
      }
    );

    return () => subscription.unsubscribe();
  }, []);

  return (
    <AuthContext.Provider value={{ user, loading }}>
      {children}
    </AuthContext.Provider>
  );
}
```

### AI Integration Architecture (ElevenLabs)

#### ElevenLabs Integration Flow
```mermaid
graph TD
    A[User Creates Agent] --> B[CallSaver API]
    B --> C[ElevenLabs API]
    C --> D[Agent Created]
    D --> E[User Imports Twilio Number]
    E --> F[ElevenLabs Configures Webhooks]
    F --> G[Call Received]
    G --> H[ElevenLabs Processes Call]
    H --> I[Webhook to CallSaver]
    I --> J[Update Database]
    J --> K[Real-time UI Update]
```

#### Agent Management API
```javascript
// /api/agents/create.js
export async function POST(request) {
  const { name, prompt, voiceId, llmConfig } = await request.json();
  
  // Create agent in ElevenLabs
  const elevenlabsAgent = await createElevenlabsAgent({
    name,
    prompt,
    voice_id: voiceId,
    llm: llmConfig
  });
  
  // Store in database
  const agent = await supabase
    .from('ai_agents')
    .insert({
      user_id: user.id,
      elevenlabs_agent_id: elevenlabsAgent.id,
      name,
      prompt,
      voice_id: voiceId,
      llm_config: llmConfig
    })
    .select()
    .single();
  
  return Response.json(agent);
}
```

#### Webhook Handler
```javascript
// /api/webhooks/elevenlabs.js
export async function POST(request) {
  const signature = request.headers.get('elevenlabs-signature');
  const body = await request.text();
  
  // Verify webhook signature
  if (!verifyWebhookSignature(body, signature)) {
    return new Response('Unauthorized', { status: 401 });
  }
  
  const event = JSON.parse(body);
  
  switch (event.type) {
    case 'conversation.ended':
      await handleConversationEnded(event.data);
      break;
    default:
      console.log('Unhandled event:', event.type);
  }
  
  return new Response('OK');
}
```

## Performance Architecture

### Caching Strategy
```mermaid
graph TD
    A[User Request] --> B{Cache Layer}
    B -->|Hit| C[Return Cached Data]
    B -->|Miss| D[Fetch from Database]
    D --> E[Update Cache]
    E --> F[Return Data]
    
    subgraph "Cache Layers"
        G[Browser Cache]
        H[CDN Cache]
        I[Application Cache]
        J[Database Cache]
    end
```

### Performance Optimizations
1. **Code Splitting**: Automatic route-based splitting
2. **Image Optimization**: Next.js Image component
3. **Bundle Analysis**: Regular bundle size monitoring
4. **Database Indexing**: Strategic index placement
5. **Real-time Updates**: Supabase subscriptions

### Monitoring and Observability
```javascript
// Performance monitoring setup
import { Analytics } from '@vercel/analytics/react';
import * as Sentry from '@sentry/nextjs';

// Error tracking
Sentry.init({
  dsn: process.env.NEXT_PUBLIC_SENTRY_DSN,
  tracesSampleRate: 1.0,
});

// Performance analytics
export default function App({ Component, pageProps }) {
  return (
    <>
      <Component {...pageProps} />
      <Analytics />
    </>
  );
}
```

## Security Architecture

### Multi-Layer Security
```mermaid
graph TD
    A[User Request] --> B[Security Headers]
    B --> C[Authentication Check]
    C --> D[Input Validation]
    D --> E[Rate Limiting]
    E --> F[RLS Policies]
    F --> G[Application Logic]
    G --> H[Audit Logging]
```

### Security Implementation
```javascript
// Security headers configuration
const securityHeaders = [
  {
    key: 'X-DNS-Prefetch-Control',
    value: 'on'
  },
  {
    key: 'Strict-Transport-Security',
    value: 'max-age=63072000; includeSubDomains; preload'
  },
  {
    key: 'X-Frame-Options',
    value: 'DENY'
  },
  {
    key: 'X-Content-Type-Options',
    value: 'nosniff'
  },
  {
    key: 'Referrer-Policy',
    value: 'origin-when-cross-origin'
  }
];

// Input validation with Zod
const createAgentSchema = z.object({
  name: z.string().min(1).max(100),
  prompt: z.string().min(10).max(5000),
  voiceId: z.string().uuid(),
  llmConfig: z.object({
    provider: z.enum(['openai', 'anthropic', 'google']),
    model: z.string()
  })
});
```

## Deployment Architecture

### Modern Deployment Strategy
```mermaid
graph TD
    A[Git Push] --> B[Vercel Build]
    B --> C[Automated Tests]
    C --> D[Security Scan]
    D --> E[Deploy to Preview]
    E --> F[Manual Approval]
    F --> G[Deploy to Production]
    G --> H[Health Checks]
    H --> I[Monitoring Active]
```

### Infrastructure Components
1. **Frontend**: Vercel (Next.js optimized)
2. **Database**: Supabase (managed PostgreSQL)
3. **AI Services**: ElevenLabs (managed AI)
4. **Monitoring**: Vercel Analytics + Sentry
5. **CDN**: Vercel Edge Network

## Scalability Architecture

### Horizontal Scaling Strategy
```mermaid
graph TD
    A[Load Balancer] --> B[Next.js Instance 1]
    A --> C[Next.js Instance 2]
    A --> D[Next.js Instance N]
    
    B --> E[Supabase Connection Pool]
    C --> E
    D --> E
    
    E --> F[PostgreSQL Primary]
    E --> G[PostgreSQL Replicas]
```

### Auto-scaling Configuration
- **Vercel Functions**: Automatic scaling based on demand
- **Database**: Supabase connection pooling and read replicas
- **CDN**: Global edge network for static assets
- **Monitoring**: Real-time scaling metrics

## Migration Strategy

### Phased Migration Approach
1. **Phase 1**: Set up new infrastructure
2. **Phase 2**: Migrate database and consolidate
3. **Phase 3**: Replace authentication system
4. **Phase 4**: Integrate ElevenLabs and remove Twilio features
5. **Phase 5**: Modernize frontend with new state management
6. **Phase 6**: Performance optimization and monitoring

### Risk Mitigation
- **Blue-Green Deployment**: Zero-downtime deployments
- **Feature Flags**: Gradual feature rollout
- **Rollback Procedures**: Quick rollback capabilities
- **Comprehensive Testing**: Automated testing at every level

---

**Next Document**: [data-flow-diagrams.md](./data-flow-diagrams.md)
