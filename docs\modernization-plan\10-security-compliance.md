# Security and Compliance Strategy
## CallSaver.app - Comprehensive Security Hardening and Regulatory Compliance

### Executive Summary

This document outlines the comprehensive security and compliance strategy for CallSaver.app modernization, implementing defense-in-depth security principles, regulatory compliance requirements, and proactive threat management. The strategy ensures data protection, user privacy, and system integrity while maintaining operational efficiency.

### Security Architecture Overview

#### Defense-in-Depth Model
```mermaid
graph TB
    subgraph "Perimeter Security"
        WAF[Web Application Firewall]
        DDoS[DDoS Protection]
        CDN[CDN Security]
    end

    subgraph "Network Security"
        LB[Load Balancer + SSL]
        VPC[Virtual Private Cloud]
        FW[Network Firewall]
    end

    subgraph "Application Security"
        AUTH[Authentication]
        AUTHZ[Authorization]
        INPUT[Input Validation]
        CSRF[CSRF Protection]
    end

    subgraph "Data Security"
        ENC[Encryption at Rest]
        TLS[Encryption in Transit]
        BACKUP[Secure Backups]
        AUDIT[Audit Logging]
    end

    subgraph "Infrastructure Security"
        CONT[Container Security]
        SECRETS[Secret Management]
        RBAC[Role-Based Access]
        MONITOR[Security Monitoring]
    end

    WAF --> LB
    DDoS --> LB
    CDN --> WAF
    LB --> AUTH
    VPC --> FW
    AUTH --> AUTHZ
    AUTHZ --> INPUT
    INPUT --> ENC
    ENC --> TLS
    CONT --> SECRETS
    SECRETS --> RBAC
    RBAC --> MONITOR
```

#### Security Principles
```typescript
// Security configuration constants
export const SECURITY_CONFIG = {
  authentication: {
    sessionTimeout: 30 * 60 * 1000, // 30 minutes
    maxLoginAttempts: 5,
    lockoutDuration: 15 * 60 * 1000, // 15 minutes
    passwordMinLength: 12,
    requireMFA: true
  },
  encryption: {
    algorithm: 'AES-256-GCM',
    keyRotationInterval: 90 * 24 * 60 * 60 * 1000, // 90 days
    saltRounds: 12
  },
  headers: {
    hsts: 'max-age=31536000; includeSubDomains; preload',
    csp: "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'",
    xFrameOptions: 'DENY',
    xContentTypeOptions: 'nosniff',
    referrerPolicy: 'strict-origin-when-cross-origin'
  },
  rateLimit: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 100,
    skipSuccessfulRequests: false
  }
} as const
```

### Authentication and Authorization Security

#### Enhanced Authentication Implementation
```typescript
// Enhanced authentication service with security features
// services/SecureAuthService.ts
import bcrypt from 'bcrypt'
import speakeasy from 'speakeasy'
import { createClient } from '@supabase/supabase-js'
import { RateLimiter } from 'limiter'

interface LoginAttempt {
  email: string
  ip: string
  timestamp: Date
  success: boolean
  userAgent: string
}

export class SecureAuthService {
  private supabase = createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!
  )
  private loginLimiter = new RateLimiter(5, 'hour') // 5 attempts per hour
  private ipLimiter = new RateLimiter(20, 'hour') // 20 attempts per IP per hour

  async authenticateUser(email: string, password: string, ip: string, userAgent: string): Promise<AuthResult> {
    // Rate limiting checks
    if (!this.loginLimiter.tryRemoveTokens(1)) {
      await this.logSecurityEvent('rate_limit_exceeded', { email, ip, type: 'user' })
      throw new SecurityError('Too many login attempts. Please try again later.')
    }

    if (!this.ipLimiter.tryRemoveTokens(1)) {
      await this.logSecurityEvent('rate_limit_exceeded', { email, ip, type: 'ip' })
      throw new SecurityError('Too many requests from this IP address.')
    }

    try {
      // Check for account lockout
      const lockoutStatus = await this.checkAccountLockout(email)
      if (lockoutStatus.isLocked) {
        await this.logSecurityEvent('login_attempt_locked_account', { email, ip })
        throw new SecurityError('Account is temporarily locked due to multiple failed attempts.')
      }

      // Authenticate with Supabase
      const { data, error } = await this.supabase.auth.signInWithPassword({
        email,
        password
      })

      if (error || !data.user) {
        await this.recordFailedAttempt(email, ip, userAgent)
        await this.logSecurityEvent('login_failed', { email, ip, error: error?.message })
        throw new AuthenticationError('Invalid credentials')
      }

      // Check if MFA is required
      const user = await this.getUserProfile(data.user.id)
      if (user.mfaEnabled && !user.mfaVerified) {
        return {
          success: false,
          requiresMFA: true,
          tempToken: await this.generateTempToken(data.user.id)
        }
      }

      // Successful login
      await this.recordSuccessfulLogin(email, ip, userAgent)
      await this.logSecurityEvent('login_success', { email, ip, userId: data.user.id })

      return {
        success: true,
        user: data.user,
        session: data.session
      }
    } catch (error) {
      await this.recordFailedAttempt(email, ip, userAgent)
      throw error
    }
  }

  async verifyMFA(tempToken: string, mfaCode: string, ip: string): Promise<AuthResult> {
    const tokenData = await this.validateTempToken(tempToken)
    if (!tokenData) {
      await this.logSecurityEvent('mfa_invalid_token', { ip })
      throw new SecurityError('Invalid or expired MFA token')
    }

    const user = await this.getUserProfile(tokenData.userId)
    const verified = speakeasy.totp.verify({
      secret: user.mfaSecret,
      encoding: 'base32',
      token: mfaCode,
      window: 2 // Allow 2 time steps (60 seconds) of drift
    })

    if (!verified) {
      await this.logSecurityEvent('mfa_failed', { userId: tokenData.userId, ip })
      throw new AuthenticationError('Invalid MFA code')
    }

    // Complete authentication
    const { data, error } = await this.supabase.auth.getUser(tokenData.accessToken)
    if (error || !data.user) {
      throw new AuthenticationError('Authentication failed')
    }

    await this.logSecurityEvent('mfa_success', { userId: data.user.id, ip })
    return {
      success: true,
      user: data.user,
      session: { access_token: tokenData.accessToken }
    }
  }

  private async recordFailedAttempt(email: string, ip: string, userAgent: string) {
    await this.supabase.from('login_attempts').insert({
      email,
      ip,
      user_agent: userAgent,
      success: false,
      timestamp: new Date().toISOString()
    })

    // Check if account should be locked
    const recentAttempts = await this.getRecentFailedAttempts(email)
    if (recentAttempts >= SECURITY_CONFIG.authentication.maxLoginAttempts) {
      await this.lockAccount(email)
    }
  }

  private async checkAccountLockout(email: string): Promise<{ isLocked: boolean; lockedUntil?: Date }> {
    const { data } = await this.supabase
      .from('account_lockouts')
      .select('locked_until')
      .eq('email', email)
      .single()

    if (!data) return { isLocked: false }

    const lockedUntil = new Date(data.locked_until)
    const isLocked = lockedUntil > new Date()

    return { isLocked, lockedUntil }
  }

  private async logSecurityEvent(event: string, metadata: Record<string, any>) {
    await this.supabase.from('security_events').insert({
      event_type: event,
      metadata,
      timestamp: new Date().toISOString(),
      severity: this.getEventSeverity(event)
    })
  }

  private getEventSeverity(event: string): 'low' | 'medium' | 'high' | 'critical' {
    const severityMap: Record<string, 'low' | 'medium' | 'high' | 'critical'> = {
      'login_success': 'low',
      'login_failed': 'medium',
      'rate_limit_exceeded': 'high',
      'login_attempt_locked_account': 'high',
      'mfa_failed': 'high',
      'suspicious_activity': 'critical'
    }
    return severityMap[event] || 'medium'
  }
}

class SecurityError extends Error {
  constructor(message: string) {
    super(message)
    this.name = 'SecurityError'
  }
}

class AuthenticationError extends Error {
  constructor(message: string) {
    super(message)
    this.name = 'AuthenticationError'
  }
}
```

#### Role-Based Access Control (RBAC)
```typescript
// RBAC implementation
// services/AuthorizationService.ts
export enum Permission {
  // User management
  READ_USERS = 'users:read',
  WRITE_USERS = 'users:write',
  DELETE_USERS = 'users:delete',

  // Phone number management
  READ_PHONE_NUMBERS = 'phone_numbers:read',
  WRITE_PHONE_NUMBERS = 'phone_numbers:write',
  DELETE_PHONE_NUMBERS = 'phone_numbers:delete',

  // Call logs
  READ_CALL_LOGS = 'call_logs:read',
  EXPORT_CALL_LOGS = 'call_logs:export',

  // AI configuration
  READ_AI_CONFIG = 'ai_config:read',
  WRITE_AI_CONFIG = 'ai_config:write',

  // Admin functions
  READ_ANALYTICS = 'analytics:read',
  MANAGE_BILLING = 'billing:manage',
  SYSTEM_ADMIN = 'system:admin'
}

export enum Role {
  USER = 'user',
  PREMIUM_USER = 'premium_user',
  ADMIN = 'admin',
  SUPER_ADMIN = 'super_admin'
}

const ROLE_PERMISSIONS: Record<Role, Permission[]> = {
  [Role.USER]: [
    Permission.READ_PHONE_NUMBERS,
    Permission.WRITE_PHONE_NUMBERS,
    Permission.READ_CALL_LOGS,
    Permission.READ_AI_CONFIG,
    Permission.WRITE_AI_CONFIG
  ],
  [Role.PREMIUM_USER]: [
    ...ROLE_PERMISSIONS[Role.USER],
    Permission.DELETE_PHONE_NUMBERS,
    Permission.EXPORT_CALL_LOGS
  ],
  [Role.ADMIN]: [
    ...ROLE_PERMISSIONS[Role.PREMIUM_USER],
    Permission.READ_USERS,
    Permission.READ_ANALYTICS,
    Permission.MANAGE_BILLING
  ],
  [Role.SUPER_ADMIN]: [
    ...ROLE_PERMISSIONS[Role.ADMIN],
    Permission.WRITE_USERS,
    Permission.DELETE_USERS,
    Permission.SYSTEM_ADMIN
  ]
}

export class AuthorizationService {
  static hasPermission(userRole: Role, requiredPermission: Permission): boolean {
    const userPermissions = ROLE_PERMISSIONS[userRole] || []
    return userPermissions.includes(requiredPermission)
  }

  static requirePermission(requiredPermission: Permission) {
    return (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
      const userRole = req.user?.role as Role

      if (!userRole || !this.hasPermission(userRole, requiredPermission)) {
        return res.status(403).json({
          success: false,
          error: 'Insufficient permissions',
          required: requiredPermission
        })
      }

      next()
    }
  }

  static requireAnyPermission(permissions: Permission[]) {
    return (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
      const userRole = req.user?.role as Role

      if (!userRole || !permissions.some(permission => this.hasPermission(userRole, permission))) {
        return res.status(403).json({
          success: false,
          error: 'Insufficient permissions',
          required: permissions
        })
      }

      next()
    }
  }
}

// Usage in routes
router.get('/admin/users',
  requireAuth,
  AuthorizationService.requirePermission(Permission.READ_USERS),
  getUsersHandler
)

router.delete('/phone-numbers/:id',
  requireAuth,
  AuthorizationService.requirePermission(Permission.DELETE_PHONE_NUMBERS),
  deletePhoneNumberHandler
)
```

### Data Protection and Encryption

#### Encryption at Rest and in Transit
```typescript
// Data encryption service
// services/EncryptionService.ts
import crypto from 'crypto'
import { promisify } from 'util'

export class EncryptionService {
  private static readonly ALGORITHM = 'aes-256-gcm'
  private static readonly KEY_LENGTH = 32
  private static readonly IV_LENGTH = 16
  private static readonly TAG_LENGTH = 16

  private static getEncryptionKey(): Buffer {
    const key = process.env.ENCRYPTION_KEY
    if (!key) {
      throw new Error('ENCRYPTION_KEY environment variable is required')
    }
    return Buffer.from(key, 'hex')
  }

  static encrypt(plaintext: string): EncryptedData {
    const key = this.getEncryptionKey()
    const iv = crypto.randomBytes(this.IV_LENGTH)
    const cipher = crypto.createCipher(this.ALGORITHM, key)
    cipher.setAAD(Buffer.from('CallSaver-Auth-Data'))

    let encrypted = cipher.update(plaintext, 'utf8', 'hex')
    encrypted += cipher.final('hex')
    const tag = cipher.getAuthTag()

    return {
      encrypted,
      iv: iv.toString('hex'),
      tag: tag.toString('hex')
    }
  }

  static decrypt(encryptedData: EncryptedData): string {
    const key = this.getEncryptionKey()
    const decipher = crypto.createDecipher(this.ALGORITHM, key)

    decipher.setAuthTag(Buffer.from(encryptedData.tag, 'hex'))
    decipher.setAAD(Buffer.from('CallSaver-Auth-Data'))

    let decrypted = decipher.update(encryptedData.encrypted, 'hex', 'utf8')
    decrypted += decipher.final('utf8')

    return decrypted
  }

  // Secure storage for sensitive data
  static async encryptSensitiveData(data: SensitiveData): Promise<string> {
    const serialized = JSON.stringify(data)
    const encrypted = this.encrypt(serialized)
    return Buffer.from(JSON.stringify(encrypted)).toString('base64')
  }

  static async decryptSensitiveData(encryptedString: string): Promise<SensitiveData> {
    const encryptedData = JSON.parse(Buffer.from(encryptedString, 'base64').toString())
    const decrypted = this.decrypt(encryptedData)
    return JSON.parse(decrypted)
  }
}

interface EncryptedData {
  encrypted: string
  iv: string
  tag: string
}

interface SensitiveData {
  twilioAuthToken?: string
  elevenLabsApiKey?: string
  userSecrets?: Record<string, string>
}

// Secure credential storage
export class SecureCredentialStore {
  async storeTwilioCredentials(userId: string, accountSid: string, authToken: string): Promise<void> {
    const encryptedToken = EncryptionService.encrypt(authToken)

    await supabase.from('twilio_credentials').upsert({
      user_id: userId,
      account_sid: accountSid,
      auth_token_encrypted: JSON.stringify(encryptedToken),
      updated_at: new Date().toISOString()
    })
  }

  async getTwilioCredentials(userId: string): Promise<{ accountSid: string; authToken: string } | null> {
    const { data, error } = await supabase
      .from('twilio_credentials')
      .select('account_sid, auth_token_encrypted')
      .eq('user_id', userId)
      .single()

    if (error || !data) return null

    const encryptedData = JSON.parse(data.auth_token_encrypted)
    const authToken = EncryptionService.decrypt(encryptedData)

    return {
      accountSid: data.account_sid,
      authToken
    }
  }
}
```

#### Database Security Configuration
```sql
-- Enhanced database security policies
-- Row Level Security (RLS) policies for all tables

-- User profiles - users can only access their own data
CREATE POLICY "user_profiles_isolation" ON public.user_profiles
  FOR ALL USING (auth.uid() = id);

-- Twilio credentials - strict access control
CREATE POLICY "twilio_credentials_isolation" ON public.twilio_credentials
  FOR ALL USING (auth.uid() = user_id);

-- Call logs - users can only see their own calls
CREATE POLICY "call_logs_isolation" ON public.call_logs
  FOR ALL USING (auth.uid() = user_id);

-- Security events - admin only access
CREATE POLICY "security_events_admin_only" ON public.security_events
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.user_profiles
      WHERE id = auth.uid()
      AND role IN ('admin', 'super_admin')
    )
  );

-- Audit logs - admin read-only
CREATE POLICY "audit_logs_admin_read" ON public.audit_logs
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.user_profiles
      WHERE id = auth.uid()
      AND role IN ('admin', 'super_admin')
    )
  );

-- Enable RLS on all sensitive tables
ALTER TABLE public.user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.twilio_credentials ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.phone_numbers ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.call_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.ai_agents ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.security_events ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.audit_logs ENABLE ROW LEVEL SECURITY;

-- Create audit trigger function
CREATE OR REPLACE FUNCTION audit_trigger_function()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.audit_logs (
    table_name,
    operation,
    old_data,
    new_data,
    user_id,
    timestamp
  ) VALUES (
    TG_TABLE_NAME,
    TG_OP,
    CASE WHEN TG_OP = 'DELETE' THEN row_to_json(OLD) ELSE NULL END,
    CASE WHEN TG_OP IN ('INSERT', 'UPDATE') THEN row_to_json(NEW) ELSE NULL END,
    auth.uid(),
    NOW()
  );

  RETURN CASE WHEN TG_OP = 'DELETE' THEN OLD ELSE NEW END;
END;
$$ LANGUAGE plpgsql;

-- Apply audit triggers to sensitive tables
CREATE TRIGGER audit_user_profiles
  AFTER INSERT OR UPDATE OR DELETE ON public.user_profiles
  FOR EACH ROW EXECUTE FUNCTION audit_trigger_function();

CREATE TRIGGER audit_twilio_credentials
  AFTER INSERT OR UPDATE OR DELETE ON public.twilio_credentials
  FOR EACH ROW EXECUTE FUNCTION audit_trigger_function();
```

### Input Validation and Sanitization

#### Comprehensive Input Validation
```typescript
// Input validation and sanitization
// middleware/validation.ts
import { z } from 'zod'
import DOMPurify from 'isomorphic-dompurify'
import validator from 'validator'

// Custom validation schemas
const phoneNumberSchema = z.string().regex(/^\+[1-9]\d{1,14}$/, 'Invalid phone number format')
const emailSchema = z.string().email('Invalid email format').max(254)
const nameSchema = z.string().min(1).max(100).regex(/^[a-zA-Z\s'-]+$/, 'Invalid name format')
const areaCodeSchema = z.string().length(3).regex(/^\d{3}$/, 'Area code must be 3 digits')

// SQL injection prevention
const sqlInjectionPatterns = [
  /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION|SCRIPT)\b)/i,
  /(--|\/\*|\*\/|;|'|"|`)/,
  /(\b(OR|AND)\b.*=.*)/i,
  /(INFORMATION_SCHEMA|SYSOBJECTS|SYSCOLUMNS)/i
]

// XSS prevention patterns
const xssPatterns = [
  /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
  /javascript:/gi,
  /on\w+\s*=/gi,
  /<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi
]

export class SecurityValidator {
  static validateAndSanitize(input: any, schema: z.ZodSchema): any {
    // First, sanitize the input
    const sanitized = this.sanitizeInput(input)

    // Then validate with schema
    const result = schema.safeParse(sanitized)
    if (!result.success) {
      throw new ValidationError('Input validation failed', result.error.errors)
    }

    return result.data
  }

  static sanitizeInput(input: any): any {
    if (typeof input === 'string') {
      return this.sanitizeString(input)
    } else if (Array.isArray(input)) {
      return input.map(item => this.sanitizeInput(item))
    } else if (typeof input === 'object' && input !== null) {
      const sanitized: any = {}
      for (const [key, value] of Object.entries(input)) {
        sanitized[this.sanitizeString(key)] = this.sanitizeInput(value)
      }
      return sanitized
    }
    return input
  }

  private static sanitizeString(str: string): string {
    // Remove potential SQL injection patterns
    for (const pattern of sqlInjectionPatterns) {
      if (pattern.test(str)) {
        throw new SecurityError('Potentially malicious input detected')
      }
    }

    // Remove XSS patterns
    for (const pattern of xssPatterns) {
      if (pattern.test(str)) {
        throw new SecurityError('Potentially malicious script detected')
      }
    }

    // Sanitize HTML
    const sanitized = DOMPurify.sanitize(str, {
      ALLOWED_TAGS: [],
      ALLOWED_ATTR: []
    })

    // Additional sanitization
    return validator.escape(sanitized.trim())
  }

  static validatePhoneNumber(phoneNumber: string): string {
    return this.validateAndSanitize(phoneNumber, phoneNumberSchema)
  }

  static validateEmail(email: string): string {
    return this.validateAndSanitize(email, emailSchema)
  }

  static validateName(name: string): string {
    return this.validateAndSanitize(name, nameSchema)
  }

  static validateAreaCode(areaCode: string): string {
    return this.validateAndSanitize(areaCode, areaCodeSchema)
  }
}

// Validation middleware
export const validateRequest = (schema: z.ZodSchema) => {
  return (req: Request, res: Response, next: NextFunction) => {
    try {
      req.body = SecurityValidator.validateAndSanitize(req.body, schema)
      next()
    } catch (error) {
      if (error instanceof ValidationError) {
        return res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: error.details
        })
      } else if (error instanceof SecurityError) {
        // Log security incident
        console.error('Security validation failed:', {
          ip: req.ip,
          userAgent: req.get('User-Agent'),
          body: req.body,
          error: error.message
        })

        return res.status(400).json({
          success: false,
          error: 'Invalid input format'
        })
      }

      return res.status(500).json({
        success: false,
        error: 'Internal server error'
      })
    }
  }
}

class ValidationError extends Error {
  constructor(message: string, public details: any[]) {
    super(message)
    this.name = 'ValidationError'
  }
}
```
```