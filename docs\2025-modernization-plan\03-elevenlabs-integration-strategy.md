# ElevenLabs Integration Strategy

## Executive Summary

This document outlines the strategy for integrating ElevenLabs Conversational AI to replace the current custom Twilio implementation. ElevenLabs provides a complete voice AI stack that eliminates the need for custom voice processing while offering enterprise-grade capabilities.

## ElevenLabs Platform Overview

### Core Capabilities
- **Speech-to-Text**: Fine-tuned ASR model
- **Language Models**: Support for GPT-4, <PERSON>, <PERSON>, and custom LLMs
- **Text-to-Speech**: Low-latency TTS with 5,000+ voices in 31 languages
- **Turn-Taking Model**: Human-like conversation flow
- **Native Twilio Integration**: Automatic webhook configuration

### Pricing Structure (2025)
| Plan | Price | Minutes Included | Cost per Extra Minute | Concurrency |
|------|-------|------------------|----------------------|-------------|
| Free | $0 | 15 | N/A | 4 |
| Starter | $5 | 50 | N/A | 6 |
| Creator | $22 | 250 | ~$0.12 | 10 |
| Pro | $99 | 1,100 | ~$0.11 | 20 |
| Scale | $330 | 3,600 | ~$0.10 | 30 |
| Business | $1,320 | 13,750 | $0.08 | 30 |

**Recommendation**: Start with Pro plan ($99/month) for development and testing, scale to Business plan for production.

## Current State vs. ElevenLabs

### Current Implementation Issues
```javascript
// Current implementation is simulation-only
async function generateTTS(message) {
  console.log('Generating TTS for message:', message);
  // In production: Call AWS Polly or similar service
  return {
    success: true,
    audioUrl: 'https://example.com/demo-tts.mp3' // Mock URL
  };
}
```

**Problems**:
- No real voice synthesis
- No speech recognition
- No conversation management
- Manual Twilio webhook handling
- Limited scalability

### ElevenLabs Solution
- **Complete Voice Stack**: Production-ready ASR, TTS, and conversation management
- **Automatic Twilio Integration**: No manual webhook configuration
- **Scalable**: Handles thousands of concurrent calls
- **Advanced Features**: Knowledge bases, tools, analytics
- **Multiple LLM Support**: Choose the best model for each use case

## Integration Architecture

### New Architecture Overview
```mermaid
graph TD
    A[CallSaver Frontend] --> B[CallSaver Backend]
    B --> C[ElevenLabs API]
    C --> D[User's Twilio Account]
    C --> E[LLM Provider]
    E --> F[OpenAI/Claude/Gemini]
    C --> G[ElevenLabs Webhooks]
    G --> B
    B --> H[Supabase Database]
    
    subgraph "User Responsibilities"
        I[Purchase Twilio Numbers]
        I --> D
    end
    
    subgraph "CallSaver Responsibilities"
        J[Agent Management]
        K[Business Logic]
        L[Analytics]
        M[User Interface]
    end
    
    subgraph "ElevenLabs Responsibilities"
        N[Voice AI Processing]
        O[Conversation Management]
        P[Twilio Integration]
    end
```

### Data Flow
1. **User Setup**: User purchases Twilio numbers directly from Twilio
2. **Agent Creation**: CallSaver creates ElevenLabs agents via API
3. **Number Import**: ElevenLabs imports Twilio numbers (automatic webhook setup)
4. **Call Handling**: ElevenLabs processes all voice interactions
5. **Webhooks**: ElevenLabs sends events to CallSaver for business logic
6. **Analytics**: CallSaver aggregates data for user dashboards

## Implementation Strategy

### Phase 1: ElevenLabs Account Setup
1. **Create ElevenLabs Account**: Set up business account
2. **API Key Management**: Secure API key storage
3. **Workspace Configuration**: Set up development workspace
4. **Test Agent Creation**: Create basic test agent

### Phase 2: Remove Twilio Number Purchasing
1. **Identify Twilio Features**: Audit current Twilio integration
2. **Remove Purchase Logic**: Remove number purchasing functionality
3. **Update UI**: Remove purchase interfaces
4. **Update Documentation**: Guide users to purchase directly from Twilio

### Phase 3: ElevenLabs API Integration
1. **Agent Management API**: Create, update, delete agents
2. **Phone Number Import**: Import user's Twilio numbers
3. **Conversation Handling**: Process ElevenLabs webhooks
4. **Error Handling**: Robust error handling and retry logic

### Phase 4: User Interface Updates
1. **Agent Configuration**: UI for agent settings
2. **Phone Number Management**: Import and manage numbers
3. **Conversation Analytics**: Display conversation insights
4. **Real-time Monitoring**: Live call dashboards

## API Integration Details

### ElevenLabs Agent Management

#### Create Agent
```javascript
const createAgent = async (agentConfig) => {
  const response = await fetch('https://api.elevenlabs.io/v1/convai/agents', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${ELEVENLABS_API_KEY}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      name: agentConfig.name,
      prompt: agentConfig.prompt,
      voice_id: agentConfig.voiceId,
      llm: {
        provider: 'openai',
        model: 'gpt-4'
      },
      conversation_config: {
        turn_detection: {
          type: 'server_vad'
        }
      }
    })
  });
  
  return response.json();
};
```

#### Import Twilio Number
```javascript
const importTwilioNumber = async (phoneNumber, twilioCredentials) => {
  const response = await fetch('https://api.elevenlabs.io/v1/convai/phone-numbers', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${ELEVENLABS_API_KEY}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      phone_number: phoneNumber,
      twilio_account_sid: twilioCredentials.accountSid,
      twilio_auth_token: twilioCredentials.authToken,
      agent_id: agentConfig.agentId
    })
  });
  
  return response.json();
};
```

### Webhook Handling

#### ElevenLabs Webhook Events
- `conversation.started`: Call initiated
- `conversation.ended`: Call completed
- `conversation.interrupted`: Call interrupted
- `agent.response`: Agent response generated
- `user.speech`: User speech detected

#### Webhook Handler Implementation
```javascript
// /api/webhooks/elevenlabs
export async function POST(request) {
  const signature = request.headers.get('elevenlabs-signature');
  const body = await request.text();
  
  // Verify webhook signature
  if (!verifyWebhookSignature(body, signature)) {
    return new Response('Unauthorized', { status: 401 });
  }
  
  const event = JSON.parse(body);
  
  switch (event.type) {
    case 'conversation.started':
      await handleConversationStarted(event.data);
      break;
    case 'conversation.ended':
      await handleConversationEnded(event.data);
      break;
    default:
      console.log('Unhandled event type:', event.type);
  }
  
  return new Response('OK', { status: 200 });
}
```

## Database Schema Updates

### New Tables for ElevenLabs Integration

```sql
-- ElevenLabs agents
CREATE TABLE elevenlabs_agents (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id),
  elevenlabs_agent_id VARCHAR NOT NULL,
  name VARCHAR NOT NULL,
  prompt TEXT,
  voice_id VARCHAR,
  llm_config JSONB,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Phone number to agent mapping
CREATE TABLE phone_number_agents (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  phone_number_id UUID REFERENCES phone_numbers(id),
  elevenlabs_agent_id UUID REFERENCES elevenlabs_agents(id),
  created_at TIMESTAMP DEFAULT NOW()
);

-- Conversation logs from ElevenLabs
CREATE TABLE conversations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  elevenlabs_conversation_id VARCHAR NOT NULL,
  agent_id UUID REFERENCES elevenlabs_agents(id),
  phone_number VARCHAR NOT NULL,
  caller_number VARCHAR NOT NULL,
  status VARCHAR NOT NULL,
  duration_seconds INTEGER,
  transcript TEXT,
  summary TEXT,
  sentiment VARCHAR,
  created_at TIMESTAMP DEFAULT NOW(),
  ended_at TIMESTAMP
);
```

## User Experience Changes

### Current User Flow (To Be Removed)
1. User logs into CallSaver
2. User purchases Twilio numbers through CallSaver
3. User configures basic AI settings
4. Calls are handled by simulation

### New User Flow
1. User logs into CallSaver
2. User purchases Twilio numbers directly from Twilio
3. User creates AI agents in CallSaver
4. User imports Twilio numbers into CallSaver
5. ElevenLabs automatically configures Twilio webhooks
6. Real AI conversations are handled by ElevenLabs

### UI/UX Updates Required

#### Agent Management Interface
- **Agent Builder**: Visual interface for creating agents
- **Voice Selection**: Browse and preview 5,000+ voices
- **Prompt Editor**: Rich text editor for agent prompts
- **LLM Configuration**: Choose between different AI models
- **Testing Interface**: Test agents before deployment

#### Phone Number Management
- **Import Interface**: Import Twilio numbers with credentials
- **Agent Assignment**: Assign agents to phone numbers
- **Status Monitoring**: Real-time status of phone numbers
- **Configuration**: Manage Twilio integration settings

#### Analytics Dashboard
- **Conversation Metrics**: Call volume, duration, success rates
- **AI Performance**: Response quality, user satisfaction
- **Business Insights**: Lead qualification, appointment booking
- **Real-time Monitoring**: Live call dashboard

## Migration Strategy

### Step 1: Preparation (Week 1)
- Set up ElevenLabs business account
- Create development workspace
- Generate API keys and configure environment
- Create test agents for development

### Step 2: Backend Integration (Week 2-3)
- Implement ElevenLabs API client
- Create agent management endpoints
- Implement webhook handlers
- Update database schema

### Step 3: Remove Twilio Purchasing (Week 4)
- Identify all Twilio purchasing code
- Remove purchase-related API endpoints
- Update frontend to remove purchase UI
- Add user guidance for direct Twilio purchases

### Step 4: Frontend Updates (Week 5-6)
- Build agent management interface
- Create phone number import flow
- Implement conversation analytics
- Add real-time monitoring dashboard

### Step 5: Testing & Validation (Week 7)
- End-to-end testing with real Twilio numbers
- Validate webhook handling
- Test agent performance
- User acceptance testing

## Risk Mitigation

### Technical Risks
1. **API Rate Limits**: Implement proper rate limiting and retry logic
2. **Webhook Reliability**: Implement idempotent webhook handling
3. **Data Consistency**: Ensure proper error handling and rollback
4. **Performance**: Monitor API response times and optimize

### Business Risks
1. **User Confusion**: Clear documentation and migration guides
2. **Feature Parity**: Ensure new system matches current capabilities
3. **Cost Management**: Monitor usage and provide cost alerts
4. **Vendor Lock-in**: Maintain abstraction layer for future flexibility

## Success Metrics

### Technical Metrics
- **API Response Time**: <500ms for agent operations
- **Webhook Processing**: <100ms processing time
- **Uptime**: >99.9% availability
- **Error Rate**: <1% error rate

### Business Metrics
- **User Adoption**: >90% of users migrate successfully
- **Conversation Quality**: Improved user satisfaction scores
- **Feature Usage**: Increased engagement with AI features
- **Support Tickets**: Reduced support requests related to voice AI

## Cost Analysis

### Current Costs (Estimated)
- **Development Time**: High (custom implementation)
- **Maintenance**: High (complex custom code)
- **Twilio Costs**: Variable (user-dependent)
- **Infrastructure**: Medium (custom voice processing)

### ElevenLabs Costs
- **Development Time**: Low (API integration)
- **Maintenance**: Low (managed service)
- **ElevenLabs Subscription**: $99-$1,320/month (based on usage)
- **Twilio Costs**: Same (user pays directly)

### ROI Analysis
- **Reduced Development Time**: 80% reduction in voice AI development
- **Improved Reliability**: Professional-grade voice AI
- **Enhanced Features**: Advanced AI capabilities out of the box
- **Faster Time to Market**: Weeks instead of months for new features

---

**Next Document**: [04-database-migration-plan.md](./04-database-migration-plan.md)
