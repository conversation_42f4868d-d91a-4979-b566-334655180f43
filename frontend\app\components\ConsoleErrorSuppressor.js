"use client";

import { useEffect } from 'react';

/**
 * Console Error Suppressor Component
 * Filters out known non-critical console errors to reduce noise during development
 */
const ConsoleErrorSuppressor = () => {
  useEffect(() => {
    // Only run in development mode
    if (process.env.NODE_ENV !== 'development') {
      return;
    }

    // Store original console methods
    const originalError = console.error;
    const originalWarn = console.warn;

    // List of error patterns to suppress (non-critical errors)
    const suppressPatterns = [
      /The message port closed before a response was received/,
      /MetaMask extension not found/,
      /ChromeTransport.*connectChrome error/,
      /contentscript\.bundle\.js/,
      /chrome-extension:/,
      /moz-extension:/,
    ];

    // Custom error handler
    const filteredError = (...args) => {
      const message = args.join(' ');
      
      // Check if this error should be suppressed
      const shouldSuppress = suppressPatterns.some(pattern => 
        pattern.test(message)
      );

      // Only log if not suppressed
      if (!shouldSuppress) {
        originalError.apply(console, args);
      } else {
        // Optionally log suppressed errors in a different way for debugging
        console.debug('Suppressed non-critical error:', message);
      }
    };

    // Custom warning handler
    const filteredWarn = (...args) => {
      const message = args.join(' ');
      
      // Check if this warning should be suppressed
      const shouldSuppress = suppressPatterns.some(pattern => 
        pattern.test(message)
      );

      // Only log if not suppressed
      if (!shouldSuppress) {
        originalWarn.apply(console, args);
      }
    };

    // Override console methods
    console.error = filteredError;
    console.warn = filteredWarn;

    // Cleanup function to restore original console methods
    return () => {
      console.error = originalError;
      console.warn = originalWarn;
    };
  }, []);

  // This component doesn't render anything
  return null;
};

export default ConsoleErrorSuppressor;
